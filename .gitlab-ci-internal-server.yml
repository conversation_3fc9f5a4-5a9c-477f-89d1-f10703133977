# GitLab CI/CD 配置 - 内部服务器自动化部署
# Copyright 2025 New Vector Ltd
# SPDX-License-Identifier: AGPL-3.0-only

stages:
  - validate
  - build
  - deploy-staging
  - test-staging
  - deploy-production
  - test-production
  - cleanup

variables:
  KUBECTL_VERSION: "1.28.0"
  HELM_VERSION: "3.12.0"
  NAMESPACE_STAGING: "matrix-staging"
  NAMESPACE_PRODUCTION: "matrix-internal"
  RELEASE_NAME_STAGING: "matrix-staging"
  RELEASE_NAME_PRODUCTION: "matrix-internal"

# 基础镜像配置
.base_image: &base_image
  image: alpine:3.18
  before_script:
    - apk add --no-cache curl bash jq git
    - curl -LO "https://dl.k8s.io/release/v${KUBECTL_VERSION}/bin/linux/amd64/kubectl"
    - chmod +x kubectl && mv kubectl /usr/local/bin/
    - curl -fsSL -o get_helm.sh https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3
    - chmod 700 get_helm.sh && ./get_helm.sh --version v${HELM_VERSION}
    - kubectl version --client
    - helm version

# 配置验证阶段
validate_config:
  <<: *base_image
  stage: validate
  script:
    - echo "验证配置文件语法..."
    - |
      if [[ -f "charts/matrix-stack/user_values/internal-server-example.yaml" ]]; then
        helm template test ./charts/matrix-stack -f charts/matrix-stack/user_values/internal-server-example.yaml --dry-run
        echo "✅ 配置文件语法验证通过"
      else
        echo "❌ 配置文件不存在"
        exit 1
      fi
    - echo "验证脚本语法..."
    - bash -n scripts/deploy-internal-server.sh
    - bash -n scripts/health-check-internal.sh
    - echo "✅ 脚本语法验证通过"
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

# 构建阶段
build_config:
  <<: *base_image
  stage: build
  script:
    - echo "生成部署配置..."
    - mkdir -p deployment-configs
    - |
      # 生成 staging 环境配置
      ./scripts/deploy-internal-server.sh \
        --domain "${STAGING_DOMAIN}" \
        --cloudflare-token "${CLOUDFLARE_API_TOKEN}" \
        --email "${LETSENCRYPT_EMAIL}" \
        --namespace "${NAMESPACE_STAGING}" \
        --release "${RELEASE_NAME_STAGING}" \
        --dry-run
    - echo "✅ 配置生成完成"
  artifacts:
    paths:
      - deployment-configs/
    expire_in: 1 hour
  rules:
    - if: $CI_COMMIT_BRANCH == "main"

# Staging 环境部署
deploy_staging:
  <<: *base_image
  stage: deploy-staging
  environment:
    name: staging
    url: https://element.${STAGING_DOMAIN}
  script:
    - echo "部署到 Staging 环境..."
    - echo "$KUBECONFIG_STAGING" | base64 -d > ~/.kube/config
    - chmod 600 ~/.kube/config
    - |
      ./scripts/deploy-internal-server.sh \
        --domain "${STAGING_DOMAIN}" \
        --cloudflare-token "${CLOUDFLARE_API_TOKEN}" \
        --email "${LETSENCRYPT_EMAIL}" \
        --namespace "${NAMESPACE_STAGING}" \
        --release "${RELEASE_NAME_STAGING}"
    - echo "✅ Staging 环境部署完成"
  dependencies:
    - build_config
  rules:
    - if: $CI_COMMIT_BRANCH == "main"

# Staging 环境测试
test_staging:
  <<: *base_image
  stage: test-staging
  script:
    - echo "测试 Staging 环境..."
    - echo "$KUBECONFIG_STAGING" | base64 -d > ~/.kube/config
    - chmod 600 ~/.kube/config
    - |
      ./scripts/health-check-internal.sh \
        --domain "${STAGING_DOMAIN}" \
        --namespace "${NAMESPACE_STAGING}" \
        --release "${RELEASE_NAME_STAGING}" \
        --detailed
    - echo "✅ Staging 环境测试通过"
  dependencies:
    - deploy_staging
  artifacts:
    reports:
      junit: health-report-*.xml
    paths:
      - health-report-*.txt
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH == "main"

# Production 环境部署（手动触发）
deploy_production:
  <<: *base_image
  stage: deploy-production
  environment:
    name: production
    url: https://element.${PRODUCTION_DOMAIN}
  script:
    - echo "部署到 Production 环境..."
    - echo "$KUBECONFIG_PRODUCTION" | base64 -d > ~/.kube/config
    - chmod 600 ~/.kube/config
    - |
      # 创建备份
      mkdir -p backups
      helm get values "${RELEASE_NAME_PRODUCTION}" -n "${NAMESPACE_PRODUCTION}" > "backups/values-backup-$(date +%Y%m%d-%H%M%S).yaml" || true
    - |
      ./scripts/deploy-internal-server.sh \
        --domain "${PRODUCTION_DOMAIN}" \
        --cloudflare-token "${CLOUDFLARE_API_TOKEN}" \
        --email "${LETSENCRYPT_EMAIL}" \
        --namespace "${NAMESPACE_PRODUCTION}" \
        --release "${RELEASE_NAME_PRODUCTION}" \
        --postgres-external \
        --postgres-host "${POSTGRES_HOST}" \
        --postgres-user "${POSTGRES_USER}" \
        --postgres-password "${POSTGRES_PASSWORD}"
    - echo "✅ Production 环境部署完成"
  dependencies:
    - test_staging
  artifacts:
    paths:
      - backups/
    expire_in: 30 days
  when: manual
  rules:
    - if: $CI_COMMIT_BRANCH == "main"

# Production 环境测试
test_production:
  <<: *base_image
  stage: test-production
  script:
    - echo "测试 Production 环境..."
    - echo "$KUBECONFIG_PRODUCTION" | base64 -d > ~/.kube/config
    - chmod 600 ~/.kube/config
    - |
      ./scripts/health-check-internal.sh \
        --domain "${PRODUCTION_DOMAIN}" \
        --namespace "${NAMESPACE_PRODUCTION}" \
        --release "${RELEASE_NAME_PRODUCTION}" \
        --detailed
    - echo "✅ Production 环境测试通过"
  dependencies:
    - deploy_production
  artifacts:
    reports:
      junit: health-report-*.xml
    paths:
      - health-report-*.txt
    expire_in: 30 days
  rules:
    - if: $CI_COMMIT_BRANCH == "main"

# 清理临时资源
cleanup:
  <<: *base_image
  stage: cleanup
  script:
    - echo "清理临时资源..."
    - rm -rf deployment-configs/
    - echo "✅ 清理完成"
  when: always
  rules:
    - if: $CI_COMMIT_BRANCH == "main"

# 回滚任务（手动触发）
rollback_production:
  <<: *base_image
  stage: deploy-production
  environment:
    name: production
    url: https://element.${PRODUCTION_DOMAIN}
  script:
    - echo "回滚 Production 环境..."
    - echo "$KUBECONFIG_PRODUCTION" | base64 -d > ~/.kube/config
    - chmod 600 ~/.kube/config
    - |
      # 获取上一个版本
      PREVIOUS_REVISION=$(helm history "${RELEASE_NAME_PRODUCTION}" -n "${NAMESPACE_PRODUCTION}" --max 2 -o json | jq -r '.[1].revision // empty')
      if [[ -n "$PREVIOUS_REVISION" ]]; then
        echo "回滚到版本: $PREVIOUS_REVISION"
        helm rollback "${RELEASE_NAME_PRODUCTION}" "$PREVIOUS_REVISION" -n "${NAMESPACE_PRODUCTION}"
      else
        echo "❌ 没有可回滚的版本"
        exit 1
      fi
    - echo "✅ 回滚完成"
  when: manual
  rules:
    - if: $CI_COMMIT_BRANCH == "main"

# 监控任务（定时执行）
monitoring:
  <<: *base_image
  stage: test-production
  script:
    - echo "执行监控检查..."
    - echo "$KUBECONFIG_PRODUCTION" | base64 -d > ~/.kube/config
    - chmod 600 ~/.kube/config
    - |
      ./scripts/health-check-internal.sh \
        --domain "${PRODUCTION_DOMAIN}" \
        --namespace "${NAMESPACE_PRODUCTION}" \
        --release "${RELEASE_NAME_PRODUCTION}"
    - echo "✅ 监控检查完成"
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
  artifacts:
    reports:
      junit: health-report-*.xml
    paths:
      - health-report-*.txt
    expire_in: 7 days
