# Matrix RTC/LiveKit TURN 服务配置修正指南

## 🎯 问题分析和解决方案

### 重要发现：LiveKit 默认使用 Google STUN 服务器

经过深入研究，我发现了一个关键问题：

**LiveKit 确实默认使用外部 STUN 服务器**：
- `stun.l.google.com:19302`
- `stun1.l.google.com:19302`
- 当 `ice_servers` 为空时，LiveKit 使用 SFU 提供的默认 ICE 服务器

**原有配置的错误**：
- ❌ `turn.enabled: false` - 错误地禁用了内置 TURN 服务
- ❌ 没有明确禁用外部 ICE 服务器
- ❌ 没有配置虚拟公网 IP

## 📋 正确的配置修正

### 1. 核心配置文件修正

#### A. config-underrides.yaml.tpl
```yaml
rtc:
  use_external_ip: true
  # 禁用默认的外部 ICE 服务器（包括 Google STUN）
  ice_servers: []

# 启用内置 TURN 服务器
turn:
  enabled: true
  # 使用虚拟公网 IP
  external_tls: true
```

#### B. 内部服务器配置
```yaml
matrixRTC:
  sfu:
    enabled: true
    additional: |
      rtc:
        use_external_ip: true
        # 禁用所有默认外部 ICE 服务器（包括 Google STUN）
        ice_servers: []
      
      # 启用内置 TURN 服务器
      turn:
        enabled: true
        external_tls: true
        domain: rtc.example.com
        tls_port: 5349
        udp_port: 3478
```

### 2. 虚拟公网 IP 配置

#### A. DNS TTL 设置为 60 秒
```bash
# 在 dynamic-ip-manager.sh 中
"ttl": 60  # 从 300 秒改为 60 秒
```

#### B. 动态 IP 检测配置
```yaml
dynamicIP:
  enabled: true
  dnsTTL: 60
  checkInterval: 60
```

### 3. 完整的生产环境配置

已创建 `internal-server-production.yaml`，包含：
- ✅ 启用内置 TURN 服务
- ✅ 禁用 Google STUN 服务器
- ✅ 虚拟公网 IP 配置
- ✅ DNS TTL 60 秒
- ✅ 完整的证书管理
- ✅ 资源配置和监控

## 🚀 部署和验证步骤

### 1. 部署内部服务器
```bash
# 使用修正后的配置部署
helm upgrade --install matrix-internal ./charts/matrix-stack \
  -f charts/matrix-stack/user_values/internal-server-production.yaml \
  --namespace matrix-internal \
  --create-namespace

# 设置环境变量
export CLOUDFLARE_API_TOKEN="your-cloudflare-api-token"
export CLOUDFLARE_ZONE_ID="your-zone-id"
```

### 2. 初始化动态 IP 管理
```bash
# 初始化动态 IP 管理（DNS TTL 60秒）
./scripts/dynamic-ip-manager.sh setup --domain example.com
```

### 3. 验证 TURN 配置
```bash
# 运行验证脚本
chmod +x scripts/verify-turn-configuration.sh
./scripts/verify-turn-configuration.sh --domain example.com --detailed
```

## 🔍 验证方法

### 1. 检查 TURN 服务状态
```bash
# 检查 Pod 状态
kubectl get pods -n matrix-internal -l app.kubernetes.io/name=matrix-rtc-sfu

# 检查 TURN 服务日志
kubectl logs -n matrix-internal -l app.kubernetes.io/name=matrix-rtc-sfu | grep -i turn
```

### 2. 验证无外部 STUN 连接
```bash
# 检查日志中是否有 Google STUN 连接
kubectl logs -n matrix-internal -l app.kubernetes.io/name=matrix-rtc-sfu | grep -i "stun.l.google.com"

# 应该没有输出，如果有输出说明仍在连接外部 STUN
```

### 3. 测试 TURN 功能
```bash
# 检查 TURN 端口
nc -zv rtc.example.com 5349  # TURN/TLS
nc -zuv rtc.example.com 3478 # TURN/UDP

# 检查 LiveKit 健康状态
curl https://rtc.example.com/health
```

### 4. 验证虚拟 IP 配置
```bash
# 检查 DNS 记录 TTL
dig rtc.example.com | grep -A1 "ANSWER SECTION"

# 应该显示 TTL 为 60 秒
```

## 📊 配置对比

### 修正前 vs 修正后

| 配置项 | 修正前 | 修正后 |
|--------|--------|--------|
| TURN 服务 | `enabled: false` ❌ | `enabled: true` ✅ |
| 外部 ICE 服务器 | 使用默认（Google STUN）❌ | `ice_servers: []` ✅ |
| DNS TTL | 300 秒 | 60 秒 ✅ |
| 虚拟 IP | 未配置 ❌ | `use_external_ip: true` ✅ |
| 证书管理 | 基本配置 | 完整的 cert-manager 配置 ✅ |

## 🛠️ 故障排除

### 1. TURN 服务未启动
```bash
# 检查配置
kubectl get configmap matrix-internal-matrix-rtc-sfu -n matrix-internal -o yaml

# 查找 turn.enabled: true
```

### 2. 仍连接外部 STUN
```bash
# 检查 ice_servers 配置
kubectl logs -n matrix-internal -l app.kubernetes.io/name=matrix-rtc-sfu | grep -i ice

# 确保没有外部 STUN 服务器地址
```

### 3. 虚拟 IP 不生效
```bash
# 检查动态 IP 管理器状态
./scripts/dynamic-ip-manager.sh detect --domain example.com

# 检查 DNS 记录
dig +short rtc.example.com
```

## 📈 性能优化建议

### 1. TURN 服务优化
- 启用 TURN/UDP（更好的性能）
- 配置适当的端口范围
- 使用 TLS 1.3 加密

### 2. 网络优化
- 配置 QoS 策略
- 优化防火墙规则
- 使用专用网络接口

### 3. 监控配置
- 监控 TURN 连接数
- 监控带宽使用
- 设置告警阈值

## ✅ 验证清单

### 部署前检查
- [ ] 配置文件已修正
- [ ] Cloudflare API Token 已设置
- [ ] DNS 记录已配置
- [ ] 证书配置正确

### 部署后验证
- [ ] 所有 Pod 运行正常
- [ ] TURN 服务已启用
- [ ] 无外部 STUN 连接
- [ ] 虚拟 IP 配置生效
- [ ] DNS TTL 为 60 秒
- [ ] 证书验证通过
- [ ] 功能测试通过

### 持续监控
- [ ] 动态 IP 检测正常
- [ ] DNS 更新及时
- [ ] 服务可访问性
- [ ] 性能指标正常

## 🎯 总结

通过这次修正，我们实现了：

1. **✅ 正确启用内置 TURN 服务**：`turn.enabled: true`
2. **✅ 完全禁用外部 STUN/TURN**：`ice_servers: []`
3. **✅ 虚拟公网 IP 配置**：`use_external_ip: true`
4. **✅ 快速 DNS 传播**：TTL 60 秒
5. **✅ 完整的自动化部署配置**
6. **✅ 全面的验证方法**

这个配置确保了 Matrix RTC 服务完全使用内部 TURN 服务器，不会连接任何外部 STUN/TURN 服务器，同时支持虚拟公网 IP 和快速 DNS 切换。
