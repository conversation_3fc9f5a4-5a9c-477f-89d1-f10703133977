# RouterOS API 连接故障排除指南

## 🚨 问题诊断

当RouterOS API连接失败时，请按照以下步骤进行系统性诊断：

### 快速诊断命令

```bash
# 运行自动诊断脚本
./scripts/routeros-troubleshoot.sh \
  --host 你的RouterOS_IP \
  --username 你的用户名 \
  --password 你的密码 \
  --verbose
```

## 🔍 常见问题及解决方案

### 1. 网络连通性问题

#### 问题症状
- `curl: (7) Failed to connect to host`
- `ping: cannot resolve host`
- 连接超时

#### 诊断步骤
```bash
# 1. 测试基本网络连通性
ping -c 3 你的RouterOS_IP

# 2. 测试端口连通性
telnet 你的RouterOS_IP 443
# 或使用nc
nc -zv 你的RouterOS_IP 443

# 3. 使用nmap扫描端口
nmap -p 80,443,8728,8729 你的RouterOS_IP
```

#### 解决方案
1. **检查网络连接**：确保设备在同一网络或可路由
2. **检查IP地址**：确认RouterOS设备的正确IP地址
3. **检查防火墙**：确保本地防火墙不阻止出站连接

### 2. RouterOS API服务未启用

#### 问题症状
- `curl: (7) Failed to connect`
- 端口不可达
- HTTP 404错误

#### 诊断步骤
```bash
# 检查RouterOS服务状态（需要其他方式连接到RouterOS）
# 通过SSH、Telnet或Winbox连接后执行：
/ip service print
```

#### 解决方案

**通过CLI启用API服务：**
```bash
# 启用HTTPS API
/ip service enable api-ssl
/ip service set api-ssl port=443

# 启用HTTP API（可选）
/ip service enable www
/ip service set www port=80

# 验证服务状态
/ip service print
```

**通过WebFig启用：**
1. 访问 `http://RouterOS_IP/webfig`
2. 导航到 `IP -> Services`
3. 启用 `api-ssl` 和/或 `www` 服务

### 3. 认证失败

#### 问题症状
- `HTTP 401 Unauthorized`
- `curl: (22) The requested URL returned error: 401`
- 认证错误

#### 诊断步骤
```bash
# 测试不同的认证方式
curl -k -u "admin:" "https://RouterOS_IP:443/rest/system/identity"
curl -k -u "admin:password" "https://RouterOS_IP:443/rest/system/identity"
```

#### 解决方案

**检查用户配置：**
```bash
# 在RouterOS中检查用户
/user print
/user group print

# 重置admin密码
/user set admin password=new-password

# 创建新的API用户
/user add name=api-user password=secure-password group=read
```

**常见认证问题：**
1. **空密码**：某些RouterOS版本admin用户默认无密码
2. **特殊字符**：密码中的特殊字符需要正确转义
3. **用户权限**：确保用户有API访问权限

### 4. SSL/TLS证书问题

#### 问题症状
- `curl: (35) SSL connect error`
- `curl: (51) SSL: certificate verification failed`
- SSL握手失败

#### 诊断步骤
```bash
# 测试SSL连接
openssl s_client -connect RouterOS_IP:443 -servername RouterOS_IP

# 忽略证书验证
curl -k -u "username:password" "https://RouterOS_IP:443/rest/system/identity"
```

#### 解决方案
1. **使用 -k 参数**：忽略自签名证书
2. **配置证书**：在RouterOS中配置有效的SSL证书
3. **使用HTTP**：临时使用HTTP API进行测试

### 5. 端口配置问题

#### 问题症状
- 连接被拒绝
- 端口不可达

#### 诊断步骤
```bash
# 扫描常见RouterOS端口
nmap -p 80,443,8080,8443,8728,8729 RouterOS_IP

# 测试不同端口
curl -k -u "user:pass" "https://RouterOS_IP:8443/rest/system/identity"
curl -k -u "user:pass" "http://RouterOS_IP:8080/rest/system/identity"
```

#### 解决方案
```bash
# 在RouterOS中检查和修改端口
/ip service print
/ip service set api-ssl port=8443
/ip service set www port=8080
```

### 6. 防火墙阻止连接

#### 问题症状
- 连接超时
- 连接被重置

#### 诊断步骤
```bash
# 检查RouterOS防火墙规则
/ip firewall filter print
/ip firewall nat print
```

#### 解决方案
```bash
# 添加防火墙规则允许API访问
/ip firewall filter add chain=input protocol=tcp dst-port=443 action=accept comment="Allow HTTPS API"
/ip firewall filter add chain=input protocol=tcp dst-port=80 action=accept comment="Allow HTTP API"

# 或临时禁用防火墙进行测试（不推荐生产环境）
/ip firewall filter disable [find]
```

## 🛠️ 高级故障排除

### 使用详细的curl输出

```bash
# 显示详细的连接信息
curl -k -v -u "username:password" \
  "https://RouterOS_IP:443/rest/system/identity" \
  2>&1 | tee curl-debug.log

# 分析输出
grep -E "(Connected|SSL|HTTP)" curl-debug.log
```

### 使用tcpdump/wireshark分析

```bash
# 在客户端捕获网络包
sudo tcpdump -i any -w routeros-api.pcap host RouterOS_IP

# 然后执行API调用
curl -k -u "username:password" "https://RouterOS_IP:443/rest/system/identity"
```

### RouterOS日志分析

```bash
# 在RouterOS中查看日志
/log print
/log print where topics~"web"
/log print where topics~"api"
```

## 📋 完整的测试清单

### 1. 基础连接测试
- [ ] Ping测试通过
- [ ] 端口443/80可达
- [ ] 网络路由正确

### 2. RouterOS服务检查
- [ ] api-ssl服务已启用
- [ ] www服务已启用（如果使用HTTP）
- [ ] 服务绑定到正确的接口

### 3. 认证配置检查
- [ ] 用户存在且密码正确
- [ ] 用户有API访问权限
- [ ] 用户组配置正确

### 4. 防火墙配置检查
- [ ] 防火墙允许API端口
- [ ] 没有NAT规则干扰
- [ ] 源IP地址允许访问

### 5. SSL配置检查
- [ ] SSL证书配置正确
- [ ] 可以忽略证书验证
- [ ] SSL服务正常工作

## 🔧 常用测试命令

### 基本API测试
```bash
# 系统信息
curl -k -u "user:pass" "https://IP:443/rest/system/identity"

# 接口列表
curl -k -u "user:pass" "https://IP:443/rest/interface"

# IP地址列表
curl -k -u "user:pass" "https://IP:443/rest/ip/address"

# 路由表
curl -k -u "user:pass" "https://IP:443/rest/ip/route"
```

### 服务状态检查
```bash
# 检查所有服务
curl -k -u "user:pass" "https://IP:443/rest/ip/service"

# 检查系统资源
curl -k -u "user:pass" "https://IP:443/rest/system/resource"
```

## 📞 获取帮助

如果以上步骤都无法解决问题，请：

1. **收集诊断信息**：
   ```bash
   ./scripts/routeros-troubleshoot.sh --host IP --username user --password pass > diagnostic.log 2>&1
   ```

2. **检查RouterOS版本**：确保版本支持REST API（6.45+）

3. **尝试其他连接方式**：SSH、Telnet、Winbox

4. **查看RouterOS文档**：[MikroTik官方API文档](https://help.mikrotik.com/docs/display/ROS/REST+API)

5. **联系技术支持**：提供诊断日志和具体错误信息
