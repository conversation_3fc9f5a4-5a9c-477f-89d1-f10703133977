#!/bin/bash

# RouterOS API 连接故障排除脚本
# Copyright 2025 New Vector Ltd
# SPDX-License-Identifier: AGPL-3.0-only

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_debug() { echo -e "${CYAN}[DEBUG]${NC} $1"; }

# 显示帮助信息
show_help() {
    cat << EOF
RouterOS API 连接故障排除脚本

用法: $0 [选项]

选项:
  -h, --help                  显示此帮助信息
  --host HOST                 RouterOS设备IP地址
  --username USER             RouterOS用户名 (默认: admin)
  --password PASS             RouterOS密码
  --port PORT                 RouterOS API端口 (默认: 443)
  --http                      使用HTTP而不是HTTPS
  --verbose                   显示详细输出

示例:
  $0 --host *********** --password mypassword
  $0 --host *********** --username admin --password mypassword --port 8728 --http

EOF
}

# 默认配置
ROUTEROS_HOST=""
ROUTEROS_USERNAME="admin"
ROUTEROS_PASSWORD=""
ROUTEROS_PORT="443"
USE_HTTPS="true"
VERBOSE="false"

# 参数解析
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --host)
            ROUTEROS_HOST="$2"
            shift 2
            ;;
        --username)
            ROUTEROS_USERNAME="$2"
            shift 2
            ;;
        --password)
            ROUTEROS_PASSWORD="$2"
            shift 2
            ;;
        --port)
            ROUTEROS_PORT="$2"
            shift 2
            ;;
        --http)
            USE_HTTPS="false"
            shift
            ;;
        --verbose)
            VERBOSE="true"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证必需参数
if [[ -z "$ROUTEROS_HOST" ]]; then
    log_error "缺少RouterOS主机地址，请使用 --host 参数"
    exit 1
fi

if [[ -z "$ROUTEROS_PASSWORD" ]]; then
    log_error "缺少RouterOS密码，请使用 --password 参数"
    exit 1
fi

# 设置协议和URL
if [[ "$USE_HTTPS" == "true" ]]; then
    PROTOCOL="https"
    DEFAULT_PORT="443"
else
    PROTOCOL="http"
    DEFAULT_PORT="80"
fi

BASE_URL="${PROTOCOL}://${ROUTEROS_HOST}:${ROUTEROS_PORT}"
AUTH="${ROUTEROS_USERNAME}:${ROUTEROS_PASSWORD}"

echo "=========================================="
echo "RouterOS API 连接故障排除"
echo "=========================================="
echo "目标设备: $ROUTEROS_HOST:$ROUTEROS_PORT"
echo "协议: $PROTOCOL"
echo "用户名: $ROUTEROS_USERNAME"
echo "=========================================="
echo

# 1. 基本网络连通性测试
test_network_connectivity() {
    log_info "=== 1. 基本网络连通性测试 ==="
    
    log_info "测试ping连通性..."
    if ping -c 3 -W 3 "$ROUTEROS_HOST" >/dev/null 2>&1; then
        log_success "Ping测试成功"
    else
        log_error "Ping测试失败 - 设备可能不可达或禁用了ICMP"
        log_info "尝试使用nmap检测端口..."
        if command -v nmap >/dev/null 2>&1; then
            nmap -p "$ROUTEROS_PORT" "$ROUTEROS_HOST" 2>/dev/null || log_warning "nmap检测失败"
        fi
    fi
    
    log_info "测试端口连通性..."
    if timeout 5 bash -c "</dev/tcp/$ROUTEROS_HOST/$ROUTEROS_PORT" 2>/dev/null; then
        log_success "端口 $ROUTEROS_PORT 可达"
    else
        log_error "端口 $ROUTEROS_PORT 不可达"
        log_info "常见RouterOS API端口："
        log_info "  - HTTP API: 80"
        log_info "  - HTTPS API: 443"
        log_info "  - API (binary): 8728"
        log_info "  - API-SSL (binary): 8729"
    fi
    echo
}

# 2. RouterOS服务状态检测
test_routeros_services() {
    log_info "=== 2. RouterOS服务状态检测 ==="
    
    # 检测常见的RouterOS服务端口
    local common_ports=("80" "443" "8728" "8729" "22" "23" "8291")
    
    log_info "扫描常见RouterOS服务端口..."
    for port in "${common_ports[@]}"; do
        if timeout 2 bash -c "</dev/tcp/$ROUTEROS_HOST/$port" 2>/dev/null; then
            case $port in
                80) log_success "端口 $port 开放 (HTTP/WebFig)" ;;
                443) log_success "端口 $port 开放 (HTTPS/REST API)" ;;
                8728) log_success "端口 $port 开放 (API Binary)" ;;
                8729) log_success "端口 $port 开放 (API-SSL Binary)" ;;
                22) log_success "端口 $port 开放 (SSH)" ;;
                23) log_success "端口 $port 开放 (Telnet)" ;;
                8291) log_success "端口 $port 开放 (Winbox)" ;;
            esac
        else
            log_debug "端口 $port 关闭或不可达"
        fi
    done
    echo
}

# 3. SSL/TLS证书检测
test_ssl_certificate() {
    if [[ "$USE_HTTPS" == "true" ]]; then
        log_info "=== 3. SSL/TLS证书检测 ==="
        
        log_info "检查SSL证书..."
        if command -v openssl >/dev/null 2>&1; then
            local ssl_output
            ssl_output=$(echo | timeout 5 openssl s_client -connect "$ROUTEROS_HOST:$ROUTEROS_PORT" -servername "$ROUTEROS_HOST" 2>&1)
            
            if echo "$ssl_output" | grep -q "CONNECTED"; then
                log_success "SSL连接建立成功"
                
                # 提取证书信息
                if echo "$ssl_output" | grep -q "subject="; then
                    local subject
                    subject=$(echo "$ssl_output" | grep "subject=" | head -1)
                    log_info "证书主题: $subject"
                fi
                
                if echo "$ssl_output" | grep -q "issuer="; then
                    local issuer
                    issuer=$(echo "$ssl_output" | grep "issuer=" | head -1)
                    log_info "证书颁发者: $issuer"
                fi
                
                # 检查证书验证状态
                if echo "$ssl_output" | grep -q "Verify return code: 0"; then
                    log_success "SSL证书验证通过"
                else
                    log_warning "SSL证书验证失败（自签名证书是正常的）"
                fi
            else
                log_error "SSL连接失败"
                log_info "可能的原因："
                log_info "  - RouterOS未启用HTTPS API服务"
                log_info "  - 端口配置错误"
                log_info "  - 防火墙阻止连接"
            fi
        else
            log_warning "openssl命令不可用，跳过SSL检测"
        fi
        echo
    fi
}

# 4. HTTP/HTTPS基础连接测试
test_http_connection() {
    log_info "=== 4. HTTP/HTTPS基础连接测试 ==="
    
    local test_url="${BASE_URL}/"
    log_info "测试基础HTTP连接: $test_url"
    
    local curl_args=("-s" "-w" "HTTP状态码: %{http_code}\n连接时间: %{time_connect}s\n总时间: %{time_total}s\n")
    
    if [[ "$USE_HTTPS" == "true" ]]; then
        curl_args+=("-k")  # 忽略SSL证书错误
    fi
    
    if [[ "$VERBOSE" == "true" ]]; then
        curl_args+=("-v")
    fi
    
    local response
    response=$(timeout 10 curl "${curl_args[@]}" "$test_url" 2>&1)
    local curl_exit_code=$?
    
    if [[ $curl_exit_code -eq 0 ]]; then
        log_success "HTTP连接测试成功"
        if [[ "$VERBOSE" == "true" ]]; then
            echo "$response"
        fi
    else
        log_error "HTTP连接测试失败，退出代码: $curl_exit_code"
        case $curl_exit_code in
            6) log_error "无法解析主机名" ;;
            7) log_error "无法连接到主机" ;;
            28) log_error "连接超时" ;;
            35) log_error "SSL握手失败" ;;
            51) log_error "SSL证书验证失败" ;;
            52) log_error "服务器未返回任何内容" ;;
            56) log_error "网络数据接收失败" ;;
            *) log_error "未知错误" ;;
        esac
    fi
    echo
}

# 5. RouterOS REST API认证测试
test_rest_api_auth() {
    log_info "=== 5. RouterOS REST API认证测试 ==="
    
    local api_url="${BASE_URL}/rest/system/identity"
    log_info "测试REST API认证: $api_url"
    
    local curl_args=("-s" "-w" "\nHTTP状态码: %{http_code}\n" "-u" "$AUTH")
    
    if [[ "$USE_HTTPS" == "true" ]]; then
        curl_args+=("-k")
    fi
    
    if [[ "$VERBOSE" == "true" ]]; then
        curl_args+=("-v")
    fi
    
    local response
    response=$(timeout 10 curl "${curl_args[@]}" "$api_url" 2>&1)
    local curl_exit_code=$?
    
    if [[ $curl_exit_code -eq 0 ]]; then
        local http_code
        http_code=$(echo "$response" | tail -1 | grep -o '[0-9]*')
        
        case $http_code in
            200)
                log_success "REST API认证成功"
                log_info "系统信息:"
                echo "$response" | head -n -1 | jq . 2>/dev/null || echo "$response" | head -n -1
                ;;
            401)
                log_error "认证失败 - 用户名或密码错误"
                ;;
            403)
                log_error "权限不足 - 用户没有API访问权限"
                ;;
            404)
                log_error "REST API服务未启用或路径错误"
                ;;
            *)
                log_error "API请求失败，HTTP状态码: $http_code"
                ;;
        esac
    else
        log_error "API请求失败，curl退出代码: $curl_exit_code"
    fi
    echo
}

# 6. 替代连接方法测试
test_alternative_methods() {
    log_info "=== 6. 替代连接方法测试 ==="
    
    # 测试HTTP而不是HTTPS
    if [[ "$USE_HTTPS" == "true" ]]; then
        log_info "尝试HTTP连接（端口80）..."
        local http_url="http://${ROUTEROS_HOST}:80/rest/system/identity"
        local response
        response=$(timeout 5 curl -s -w "HTTP状态码: %{http_code}\n" -u "$AUTH" "$http_url" 2>/dev/null)
        if [[ $? -eq 0 ]]; then
            log_success "HTTP API可用"
            echo "$response"
        else
            log_info "HTTP API不可用"
        fi
    fi
    
    # 测试不同的端口
    log_info "尝试其他常见API端口..."
    local alt_ports=("8080" "8443" "8728" "8729")
    for port in "${alt_ports[@]}"; do
        if [[ "$port" != "$ROUTEROS_PORT" ]]; then
            local alt_url="${PROTOCOL}://${ROUTEROS_HOST}:${port}/rest/system/identity"
            if timeout 3 curl -s -k -u "$AUTH" "$alt_url" >/dev/null 2>&1; then
                log_success "端口 $port 上的API可用"
            fi
        fi
    done
    echo
}

# 7. RouterOS配置建议
show_routeros_config() {
    log_info "=== 7. RouterOS配置建议 ==="
    
    cat << 'EOF'
如果连接仍然失败，请检查RouterOS设备配置：

1. 检查RouterOS版本 (需要v7.1beta4+)：
   /system resource print
   /system package print

2. 启用REST API服务：
   # HTTPS API (推荐)
   /ip service enable www-ssl
   /ip service set www-ssl port=443

   # HTTP API (仅测试环境，RouterOS v7.9+)
   /ip service enable www
   /ip service set www port=80

3. 检查服务状态：
   /ip service print where name~"www"

4. 创建API专用用户 (推荐)：
   # 创建只读用户组
   /user group add name=api-readonly policy=read,api,!local,!telnet,!ssh,!ftp,!reboot,!write,!policy,!test,!winbox,!password,!web,!sniff,!sensitive,!romon

   # 创建API用户
   /user add name=api-user password=secure-password group=api-readonly

5. 检查防火墙规则：
   /ip firewall filter print
   # 添加允许规则
   /ip firewall filter add chain=input protocol=tcp dst-port=443 src-address=***********/24 action=accept comment="Allow HTTPS API"

6. 检查用户权限：
   /user print
   /user group print

7. 测试本地连接：
   # 在RouterOS设备上执行
   /system identity print

官方文档参考：
- REST API: https://help.mikrotik.com/docs/spaces/ROS/pages/47579162/REST+API
- API文档: https://help.mikrotik.com/docs/spaces/ROS/pages/47579160/API
- 服务配置: https://help.mikrotik.com/docs/display/ROS/Services

常见问题解决：
- RouterOS v7.1beta4+ 才支持REST API
- 如果使用自签名证书，curl需要 -k 参数
- admin用户在某些版本中默认无密码
- 确保用户有API权限 (policy=api)
- HTTP API仅在RouterOS v7.9+中可用
- 生产环境建议使用HTTPS API
EOF
    echo
}

# 8. 生成诊断报告
generate_diagnostic_report() {
    log_info "=== 8. 生成诊断报告 ==="
    
    local report_file="routeros-diagnostic-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$report_file" << EOF
RouterOS API 连接诊断报告
生成时间: $(date)

测试配置:
- 目标主机: $ROUTEROS_HOST
- 端口: $ROUTEROS_PORT
- 协议: $PROTOCOL
- 用户名: $ROUTEROS_USERNAME

网络连通性:
- Ping测试: $(if ping -c 1 -W 3 "$ROUTEROS_HOST" >/dev/null 2>&1; then echo "成功"; else echo "失败"; fi)
- 端口连通性: $(if timeout 2 bash -c "</dev/tcp/$ROUTEROS_HOST/$ROUTEROS_PORT" 2>/dev/null; then echo "成功"; else echo "失败"; fi)

API测试:
- REST API认证: $(if timeout 5 curl -s -k -u "$AUTH" "${BASE_URL}/rest/system/identity" >/dev/null 2>&1; then echo "成功"; else echo "失败"; fi)

建议的解决步骤:
1. 检查RouterOS设备网络连接
2. 验证API服务是否启用
3. 确认用户名和密码正确
4. 检查防火墙设置
5. 尝试不同的端口和协议

EOF
    
    log_success "诊断报告已生成: $report_file"
}

# 主函数
main() {
    test_network_connectivity
    test_routeros_services
    test_ssl_certificate
    test_http_connection
    test_rest_api_auth
    test_alternative_methods
    show_routeros_config
    generate_diagnostic_report
    
    log_info "=== 故障排除完成 ==="
    log_info "如果问题仍然存在，请："
    log_info "1. 检查RouterOS设备配置"
    log_info "2. 查看生成的诊断报告"
    log_info "3. 尝试从RouterOS设备本地测试API"
}

# 运行主函数
main
