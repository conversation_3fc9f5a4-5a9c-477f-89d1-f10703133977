#!/usr/bin/env bash

# Copyright 2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only

# 验证生产环境配置文件的正确性

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置文件路径
CONFIG_FILE="charts/matrix-stack/user_values/internal-server-production.yaml"

# 验证配置文件存在
validate_file_exists() {
    log_info "验证配置文件存在性..."
    
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        return 1
    fi
    
    log_success "配置文件存在: $CONFIG_FILE"
}

# 验证 YAML 语法
validate_yaml_syntax() {
    log_info "验证 YAML 语法..."
    
    if ! yq eval '.' "$CONFIG_FILE" >/dev/null 2>&1; then
        log_error "YAML 语法错误"
        return 1
    fi
    
    log_success "YAML 语法正确"
}

# 验证 Helm 模板语法
validate_helm_template() {
    log_info "验证 Helm 模板语法..."
    
    if ! helm template test-release ./charts/matrix-stack \
        -f "$CONFIG_FILE" \
        --dry-run >/dev/null 2>&1; then
        log_error "Helm 模板验证失败"
        log_info "运行以下命令查看详细错误："
        echo "helm template test-release ./charts/matrix-stack -f $CONFIG_FILE --dry-run"
        return 1
    fi
    
    log_success "Helm 模板语法正确"
}

# 验证 TURN 服务配置
validate_turn_config() {
    log_info "验证 TURN 服务配置..."
    
    # 检查 TURN 服务是否启用
    if ! yq eval '.matrixRTC.sfu.additional.turn-config.config' "$CONFIG_FILE" | grep -q "enabled: true"; then
        log_error "TURN 服务未启用"
        return 1
    fi
    
    # 检查是否禁用外部 ICE 服务器
    if ! yq eval '.matrixRTC.sfu.additional.turn-config.config' "$CONFIG_FILE" | grep -q "ice_servers: \[\]"; then
        log_error "外部 ICE 服务器未禁用"
        return 1
    fi
    
    # 检查 TURN 域名配置
    if ! yq eval '.matrixRTC.sfu.additional.turn-config.config' "$CONFIG_FILE" | grep -q "domain: rtc.example.com"; then
        log_error "TURN 域名配置缺失"
        return 1
    fi
    
    log_success "TURN 服务配置正确"
}

# 验证域名一致性
validate_domain_consistency() {
    log_info "验证域名配置一致性..."
    
    local domains=(
        "$(yq eval '.synapse.ingress.host' "$CONFIG_FILE")"
        "$(yq eval '.matrixAuthenticationService.ingress.host' "$CONFIG_FILE")"
        "$(yq eval '.matrixRTC.ingress.host' "$CONFIG_FILE")"
        "$(yq eval '.elementWeb.ingress.host' "$CONFIG_FILE")"
    )
    
    local expected_domains=(
        "matrix.example.com"
        "mas.example.com"
        "rtc.example.com"
        "element.example.com"
    )
    
    for i in "${!domains[@]}"; do
        if [[ "${domains[$i]}" != "${expected_domains[$i]}" ]]; then
            log_error "域名配置不一致: ${domains[$i]} != ${expected_domains[$i]}"
            return 1
        fi
    done
    
    log_success "域名配置一致"
}

# 验证证书配置
validate_cert_config() {
    log_info "验证证书配置..."
    
    # 检查 certManager 配置
    if ! yq eval '.certManager.clusterIssuer' "$CONFIG_FILE" | grep -q "cloudflare-letsencrypt"; then
        log_error "certManager.clusterIssuer 配置错误"
        return 1
    fi
    
    # 检查是否有 email 配置
    if ! yq eval '.certManager.email' "$CONFIG_FILE" | grep -q "@"; then
        log_error "certManager.email 配置缺失"
        return 1
    fi
    
    # 检查所有服务的证书注解
    local services=("synapse" "matrixAuthenticationService" "matrixRTC" "elementWeb")
    
    for service in "${services[@]}"; do
        if ! yq eval ".${service}.ingress.annotations.\"cert-manager.io/cluster-issuer\"" "$CONFIG_FILE" | grep -q "cloudflare-letsencrypt"; then
            log_error "$service 的证书注解配置错误"
            return 1
        fi
    done
    
    log_success "证书配置正确"
}

# 验证资源配置
validate_resource_config() {
    log_info "验证资源配置..."
    
    local services=("synapse" "matrixAuthenticationService" "matrixRTC")
    
    for service in "${services[@]}"; do
        local requests_memory=$(yq eval ".resources.${service}.requests.memory" "$CONFIG_FILE")
        local limits_memory=$(yq eval ".resources.${service}.limits.memory" "$CONFIG_FILE")
        local requests_cpu=$(yq eval ".resources.${service}.requests.cpu" "$CONFIG_FILE")
        local limits_cpu=$(yq eval ".resources.${service}.limits.cpu" "$CONFIG_FILE")
        
        if [[ "$requests_memory" == "null" || "$limits_memory" == "null" ]]; then
            log_warning "$service 缺少内存资源配置"
        fi
        
        if [[ "$requests_cpu" == "null" || "$limits_cpu" == "null" ]]; then
            log_warning "$service 缺少 CPU 资源配置"
        fi
    done
    
    log_success "资源配置检查完成"
}

# 验证端口配置
validate_port_config() {
    log_info "验证端口配置..."
    
    # 检查 exposedServices 端口配置
    local rtc_tcp_port=$(yq eval '.matrixRTC.sfu.exposedServices.rtcTcp.port' "$CONFIG_FILE")
    local rtc_udp_port=$(yq eval '.matrixRTC.sfu.exposedServices.rtcMuxedUdp.port' "$CONFIG_FILE")
    
    if [[ "$rtc_tcp_port" == "null" || "$rtc_udp_port" == "null" ]]; then
        log_error "RTC 端口配置缺失"
        return 1
    fi
    
    # 检查端口范围是否合理（NodePort 范围：30000-32767）
    if [[ "$rtc_tcp_port" -lt 30000 || "$rtc_tcp_port" -gt 32767 ]]; then
        log_error "RTC TCP 端口超出 NodePort 范围: $rtc_tcp_port"
        return 1
    fi
    
    if [[ "$rtc_udp_port" -lt 30000 || "$rtc_udp_port" -gt 32767 ]]; then
        log_error "RTC UDP 端口超出 NodePort 范围: $rtc_udp_port"
        return 1
    fi
    
    log_success "端口配置正确"
}

# 生成配置验证报告
generate_validation_report() {
    log_info "生成配置验证报告..."
    
    local report_file="/tmp/config-validation-report-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$report_file" << EOF
Matrix 生产环境配置验证报告
========================

验证时间: $(date)
配置文件: $CONFIG_FILE

验证结果:
EOF
    
    # 重新运行所有验证并记录结果
    {
        echo "1. 文件存在性验证:"
        validate_file_exists && echo "   ✓ 通过" || echo "   ✗ 失败"
        
        echo "2. YAML 语法验证:"
        validate_yaml_syntax && echo "   ✓ 通过" || echo "   ✗ 失败"
        
        echo "3. Helm 模板验证:"
        validate_helm_template && echo "   ✓ 通过" || echo "   ✗ 失败"
        
        echo "4. TURN 服务配置验证:"
        validate_turn_config && echo "   ✓ 通过" || echo "   ✗ 失败"
        
        echo "5. 域名一致性验证:"
        validate_domain_consistency && echo "   ✓ 通过" || echo "   ✗ 失败"
        
        echo "6. 证书配置验证:"
        validate_cert_config && echo "   ✓ 通过" || echo "   ✗ 失败"
        
        echo "7. 资源配置验证:"
        validate_resource_config && echo "   ✓ 通过" || echo "   ✗ 失败"
        
        echo "8. 端口配置验证:"
        validate_port_config && echo "   ✓ 通过" || echo "   ✗ 失败"
    } >> "$report_file" 2>&1
    
    log_success "验证报告已生成: $report_file"
}

# 主验证流程
main() {
    log_info "开始验证生产环境配置文件..."
    
    local exit_code=0
    
    # 执行所有验证
    validate_file_exists || exit_code=1
    validate_yaml_syntax || exit_code=1
    validate_helm_template || exit_code=1
    validate_turn_config || exit_code=1
    validate_domain_consistency || exit_code=1
    validate_cert_config || exit_code=1
    validate_resource_config || exit_code=1
    validate_port_config || exit_code=1
    
    # 生成报告
    generate_validation_report
    
    if [[ $exit_code -eq 0 ]]; then
        log_success "所有配置验证通过！"
    else
        log_error "部分配置验证失败，请检查配置。"
    fi
    
    return $exit_code
}

# 检查依赖工具
check_dependencies() {
    local deps=("yq" "helm")
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" >/dev/null 2>&1; then
            log_error "缺少依赖工具: $dep"
            log_info "请安装 $dep 后重试"
            exit 1
        fi
    done
}

# 显示帮助信息
show_help() {
    cat << EOF
生产环境配置验证脚本

用法: $0 [选项]

选项:
  -h, --help          显示此帮助信息
  -f, --file FILE     指定配置文件路径 (默认: $CONFIG_FILE)

示例:
  $0
  $0 -f charts/matrix-stack/user_values/custom-config.yaml

验证项目:
  1. 文件存在性
  2. YAML 语法正确性
  3. Helm 模板语法
  4. TURN 服务配置
  5. 域名配置一致性
  6. 证书配置
  7. 资源配置
  8. 端口配置
EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--file)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查依赖并执行主函数
check_dependencies
main "$@"
