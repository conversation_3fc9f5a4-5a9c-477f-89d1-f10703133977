#!/usr/bin/env bash

# Copyright 2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only

# 验证 TURN 服务配置脚本
# 确认内置 TURN 服务正常工作且未连接外部 STUN/TURN 服务器

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
TURN 服务配置验证脚本

用法: $0 [选项]

必需参数:
  --domain DOMAIN              主域名 (如: example.com)
  --namespace NAMESPACE        Kubernetes 命名空间 (默认: matrix-internal)

可选参数:
  -h, --help                  显示此帮助信息
  --release-name NAME         Helm release 名称 (默认: matrix-internal)
  --detailed                  显示详细验证信息

示例:
  $0 --domain example.com --namespace matrix-internal
  $0 --domain example.com --detailed

验证项目:
  1. 内置 TURN 服务状态
  2. 外部 STUN/TURN 服务器连接检查
  3. LiveKit 配置验证
  4. 网络连接测试
  5. 证书验证
EOF
}

# 解析命令行参数
DOMAIN=""
NAMESPACE="matrix-internal"
RELEASE_NAME="matrix-internal"
DETAILED=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --domain)
            DOMAIN="$2"
            shift 2
            ;;
        --namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        --release-name)
            RELEASE_NAME="$2"
            shift 2
            ;;
        --detailed)
            DETAILED=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证必需参数
if [[ -z "$DOMAIN" ]]; then
    log_error "必须指定域名"
    show_help
    exit 1
fi

log_info "开始验证 TURN 服务配置..."
log_info "域名: $DOMAIN"
log_info "命名空间: $NAMESPACE"
log_info "Release: $RELEASE_NAME"

# 验证 1: 检查 Matrix RTC Pod 状态
verify_pod_status() {
    log_info "验证 1: 检查 Matrix RTC Pod 状态"
    
    local pod_name
    pod_name=$(kubectl get pods -n "$NAMESPACE" -l app.kubernetes.io/name=matrix-rtc-sfu --no-headers -o custom-columns=":metadata.name" | head -n1)
    
    if [[ -z "$pod_name" ]]; then
        log_error "未找到 Matrix RTC SFU Pod"
        return 1
    fi
    
    local pod_status
    pod_status=$(kubectl get pod "$pod_name" -n "$NAMESPACE" --no-headers -o custom-columns=":status.phase")
    
    if [[ "$pod_status" == "Running" ]]; then
        log_success "Matrix RTC SFU Pod 运行正常: $pod_name"
    else
        log_error "Matrix RTC SFU Pod 状态异常: $pod_status"
        return 1
    fi
    
    # 检查 Pod 日志中的 TURN 服务启动信息
    log_info "检查 TURN 服务启动日志..."
    if kubectl logs "$pod_name" -n "$NAMESPACE" --tail=100 | grep -i "turn.*enabled\|turn.*started" >/dev/null; then
        log_success "TURN 服务已启用"
    else
        log_warning "未在日志中找到 TURN 服务启动信息"
    fi
}

# 验证 2: 检查 LiveKit 配置
verify_livekit_config() {
    log_info "验证 2: 检查 LiveKit 配置"
    
    local config_map="${RELEASE_NAME}-matrix-rtc-sfu"
    
    if ! kubectl get configmap "$config_map" -n "$NAMESPACE" >/dev/null 2>&1; then
        log_error "未找到 LiveKit 配置 ConfigMap: $config_map"
        return 1
    fi
    
    # 检查 TURN 配置
    log_info "检查 TURN 配置..."
    local turn_config
    turn_config=$(kubectl get configmap "$config_map" -n "$NAMESPACE" -o jsonpath='{.data.config-underrides\.yaml}')
    
    if echo "$turn_config" | grep -q "enabled: true"; then
        log_success "TURN 服务已在配置中启用"
    else
        log_error "TURN 服务未在配置中启用"
        return 1
    fi
    
    # 检查 ICE 服务器配置
    log_info "检查 ICE 服务器配置..."
    if echo "$turn_config" | grep -q "ice_servers: \[\]"; then
        log_success "外部 ICE 服务器已禁用"
    else
        log_warning "未找到禁用外部 ICE 服务器的配置"
    fi
    
    if [[ "$DETAILED" == "true" ]]; then
        log_info "完整的 LiveKit 配置:"
        echo "$turn_config"
    fi
}

# 验证 3: 检查网络连接
verify_network_connectivity() {
    log_info "验证 3: 检查网络连接"
    
    # 检查 TURN 端口是否可访问
    local rtc_host="rtc.${DOMAIN}"
    
    log_info "检查 TURN/TLS 端口 (5349)..."
    if timeout 10 bash -c "</dev/tcp/$rtc_host/5349" 2>/dev/null; then
        log_success "TURN/TLS 端口 5349 可访问"
    else
        log_warning "TURN/TLS 端口 5349 不可访问（可能正常，取决于防火墙配置）"
    fi
    
    log_info "检查 TURN/UDP 端口 (3478)..."
    # UDP 连接测试较复杂，这里只做基本检查
    if nc -u -z -w3 "$rtc_host" 3478 2>/dev/null; then
        log_success "TURN/UDP 端口 3478 可访问"
    else
        log_warning "TURN/UDP 端口 3478 不可访问（可能正常，取决于防火墙配置）"
    fi
}

# 验证 4: 检查外部 STUN/TURN 连接
verify_no_external_connections() {
    log_info "验证 4: 检查是否连接外部 STUN/TURN 服务器"
    
    local pod_name
    pod_name=$(kubectl get pods -n "$NAMESPACE" -l app.kubernetes.io/name=matrix-rtc-sfu --no-headers -o custom-columns=":metadata.name" | head -n1)
    
    if [[ -z "$pod_name" ]]; then
        log_error "未找到 Matrix RTC SFU Pod"
        return 1
    fi
    
    # 检查日志中是否有外部 STUN 服务器连接
    log_info "检查日志中的外部 STUN/TURN 连接..."
    
    local external_stun_patterns=(
        "stun.l.google.com"
        "stun1.l.google.com"
        "stun2.l.google.com"
        "stun3.l.google.com"
        "stun4.l.google.com"
    )
    
    local found_external=false
    for pattern in "${external_stun_patterns[@]}"; do
        if kubectl logs "$pod_name" -n "$NAMESPACE" --tail=1000 | grep -i "$pattern" >/dev/null; then
            log_error "发现连接到外部 STUN 服务器: $pattern"
            found_external=true
        fi
    done
    
    if [[ "$found_external" == "false" ]]; then
        log_success "未发现连接到外部 STUN/TURN 服务器"
    else
        log_error "检测到连接外部 STUN/TURN 服务器，配置可能有问题"
        return 1
    fi
}

# 验证 5: 检查证书配置
verify_certificates() {
    log_info "验证 5: 检查证书配置"
    
    local rtc_host="rtc.${DOMAIN}"
    
    log_info "检查 HTTPS 证书..."
    if curl -s --max-time 10 "https://$rtc_host/health" >/dev/null 2>&1; then
        log_success "HTTPS 证书验证通过"
    else
        log_warning "HTTPS 证书验证失败或服务不可访问"
    fi
    
    # 检查证书详细信息
    if [[ "$DETAILED" == "true" ]]; then
        log_info "证书详细信息:"
        echo | openssl s_client -servername "$rtc_host" -connect "$rtc_host:443" 2>/dev/null | openssl x509 -noout -text | grep -E "(Subject:|Issuer:|Not Before:|Not After:)"
    fi
}

# 验证 6: 功能测试
verify_functionality() {
    log_info "验证 6: 功能测试"
    
    local rtc_host="rtc.${DOMAIN}"
    
    # 检查 LiveKit 健康状态
    log_info "检查 LiveKit 健康状态..."
    local health_response
    health_response=$(curl -s --max-time 10 "https://$rtc_host/health" || echo "")
    
    if [[ -n "$health_response" ]]; then
        log_success "LiveKit 健康检查通过"
        if [[ "$DETAILED" == "true" ]]; then
            log_info "健康检查响应: $health_response"
        fi
    else
        log_error "LiveKit 健康检查失败"
        return 1
    fi
}

# 生成验证报告
generate_report() {
    log_info "生成验证报告..."
    
    local report_file="/tmp/turn-verification-report-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$report_file" << EOF
TURN 服务配置验证报告
===================

验证时间: $(date)
域名: $DOMAIN
命名空间: $NAMESPACE
Release: $RELEASE_NAME

验证结果:
EOF
    
    # 重新运行所有验证并记录结果
    {
        echo "1. Pod 状态验证:"
        verify_pod_status && echo "   ✓ 通过" || echo "   ✗ 失败"
        
        echo "2. LiveKit 配置验证:"
        verify_livekit_config && echo "   ✓ 通过" || echo "   ✗ 失败"
        
        echo "3. 网络连接验证:"
        verify_network_connectivity && echo "   ✓ 通过" || echo "   ✗ 失败"
        
        echo "4. 外部连接检查:"
        verify_no_external_connections && echo "   ✓ 通过" || echo "   ✗ 失败"
        
        echo "5. 证书验证:"
        verify_certificates && echo "   ✓ 通过" || echo "   ✗ 失败"
        
        echo "6. 功能测试:"
        verify_functionality && echo "   ✓ 通过" || echo "   ✗ 失败"
    } >> "$report_file" 2>&1
    
    log_success "验证报告已生成: $report_file"
}

# 主验证流程
main() {
    local exit_code=0
    
    # 执行所有验证
    verify_pod_status || exit_code=1
    verify_livekit_config || exit_code=1
    verify_network_connectivity || exit_code=1
    verify_no_external_connections || exit_code=1
    verify_certificates || exit_code=1
    verify_functionality || exit_code=1
    
    # 生成报告
    generate_report
    
    if [[ $exit_code -eq 0 ]]; then
        log_success "所有验证通过！TURN 服务配置正确。"
    else
        log_error "部分验证失败，请检查配置。"
    fi
    
    return $exit_code
}

# 执行主函数
main "$@"
