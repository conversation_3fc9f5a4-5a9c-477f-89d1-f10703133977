# 动态IP管理配置示例
# Copyright 2025 New Vector Ltd
# SPDX-License-Identifier: AGPL-3.0-only

apiVersion: v1
kind: ConfigMap
metadata:
  name: dynamic-ip-config
  namespace: matrix-external
  labels:
    app.kubernetes.io/name: dynamic-ip-manager
    app.kubernetes.io/component: config
data:
  # 基础配置
  domain: "example.com"
  internal-port: "8443"
  check-interval: "10"
  
  # DNS服务器配置
  dns-servers: |
    *******,Cloudflare
    *******,Google
    ************,腾讯DNSPod
  
  # 备用IP检测服务
  fallback-services: |
    https://ipv4.icanhazip.com
    https://api.ipify.org
    https://checkip.amazonaws.com
  
  # 超时配置
  ping-timeout: "3"
  dns-timeout: "5"
  http-timeout: "10"
  
  # 重试配置
  dns-retries: "2"
  ping-count: "3"
  
  # 验证配置
  enable-ip-ownership-verification: "true"
  enable-ping-verification: "true"
  
  # 日志配置
  log-level: "info"
  enable-detailed-logging: "true"

---
apiVersion: v1
kind: Secret
metadata:
  name: cloudflare-api-credentials
  namespace: matrix-external
  labels:
    app.kubernetes.io/name: dynamic-ip-manager
    app.kubernetes.io/component: credentials
type: Opaque
stringData:
  # Cloudflare API Token (需要Zone:Read, DNS:Edit权限)
  api-token: "your-cloudflare-api-token-here"
  
  # 可选：Zone ID (如果不提供，脚本会自动获取)
  zone-id: "your-cloudflare-zone-id-here"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dynamic-ip-manager
  namespace: matrix-external
  labels:
    app.kubernetes.io/name: dynamic-ip-manager
    app.kubernetes.io/version: "1.0.0"
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: dynamic-ip-manager
  template:
    metadata:
      labels:
        app.kubernetes.io/name: dynamic-ip-manager
    spec:
      serviceAccountName: dynamic-ip-manager
      containers:
      - name: ip-manager
        image: alpine:3.18
        command:
        - /bin/sh
        - -c
        - |
          # 安装必需工具
          apk add --no-cache bash curl dig bind-tools jq bc
          
          # 复制脚本
          cp /scripts/dynamic-ip-manager.sh /usr/local/bin/
          chmod +x /usr/local/bin/dynamic-ip-manager.sh
          
          # 启动监控
          exec /usr/local/bin/dynamic-ip-manager.sh monitor \
            --domain "${DOMAIN}" \
            --internal-port "${INTERNAL_PORT}" \
            --check-interval "${CHECK_INTERVAL}"
        env:
        - name: DOMAIN
          valueFrom:
            configMapKeyRef:
              name: dynamic-ip-config
              key: domain
        - name: INTERNAL_PORT
          valueFrom:
            configMapKeyRef:
              name: dynamic-ip-config
              key: internal-port
        - name: CHECK_INTERVAL
          valueFrom:
            configMapKeyRef:
              name: dynamic-ip-config
              key: check-interval
        - name: CLOUDFLARE_API_TOKEN
          valueFrom:
            secretKeyRef:
              name: cloudflare-api-credentials
              key: api-token
        - name: CLOUDFLARE_ZONE_ID
          valueFrom:
            secretKeyRef:
              name: cloudflare-api-credentials
              key: zone-id
              optional: true
        volumeMounts:
        - name: scripts
          mountPath: /scripts
        - name: config
          mountPath: /config
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - "pgrep -f dynamic-ip-manager.sh"
          initialDelaySeconds: 30
          periodSeconds: 60
        readinessProbe:
          exec:
            command:
            - /usr/local/bin/dynamic-ip-manager.sh
            - detect
            - --domain
            - "${DOMAIN}"
          initialDelaySeconds: 10
          periodSeconds: 30
      volumes:
      - name: scripts
        configMap:
          name: dynamic-ip-scripts
          defaultMode: 0755
      - name: config
        configMap:
          name: dynamic-ip-config
      restartPolicy: Always

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: dynamic-ip-manager
  namespace: matrix-external
  labels:
    app.kubernetes.io/name: dynamic-ip-manager

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: dynamic-ip-manager
  namespace: matrix-external
rules:
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "create", "update", "patch"]
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: dynamic-ip-manager
  namespace: matrix-external
subjects:
- kind: ServiceAccount
  name: dynamic-ip-manager
  namespace: matrix-external
roleRef:
  kind: Role
  name: dynamic-ip-manager
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: dynamic-ip-scripts
  namespace: matrix-external
  labels:
    app.kubernetes.io/name: dynamic-ip-manager
    app.kubernetes.io/component: scripts
data:
  dynamic-ip-manager.sh: |
    # 这里应该包含完整的脚本内容
    # 在实际部署时，需要将scripts/dynamic-ip-manager.sh的内容复制到这里
    # 或者使用其他方式挂载脚本文件

---
# 监控配置 (可选)
apiVersion: v1
kind: Service
metadata:
  name: dynamic-ip-manager-metrics
  namespace: matrix-external
  labels:
    app.kubernetes.io/name: dynamic-ip-manager
    app.kubernetes.io/component: metrics
spec:
  selector:
    app.kubernetes.io/name: dynamic-ip-manager
  ports:
  - name: metrics
    port: 8080
    targetPort: 8080
  type: ClusterIP

---
# ServiceMonitor for Prometheus (可选)
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: dynamic-ip-manager
  namespace: matrix-external
  labels:
    app.kubernetes.io/name: dynamic-ip-manager
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: dynamic-ip-manager
      app.kubernetes.io/component: metrics
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
