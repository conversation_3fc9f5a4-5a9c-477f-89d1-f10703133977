#!/bin/bash

# Copyright 2025 New Vector Ltd
# SPDX-License-Identifier: AGPL-3.0-only

# 内部服务器自动化部署脚本

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
NAMESPACE="${NAMESPACE:-matrix-internal}"
RELEASE_NAME="${RELEASE_NAME:-matrix-internal}"
CONFIG_DIR="${PROJECT_ROOT}/deployment-configs"
BACKUP_DIR="${PROJECT_ROOT}/backups"

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示帮助信息
show_help() {
    cat << EOF
内部服务器自动化部署脚本

用法: $0 [选项]

必需参数:
  --domain DOMAIN              主域名 (如: example.com)
  --cloudflare-token TOKEN     Cloudflare API Token
  --email EMAIL               Let's Encrypt 邮箱地址

可选参数:
  -h, --help                  显示此帮助信息
  -n, --namespace NAME        Kubernetes 命名空间 (默认: matrix-internal)
  -r, --release NAME          Helm release 名称 (默认: matrix-internal)
  --internal-port PORT        内部服务器端口 (默认: 8443)
  --postgres-external         使用外部 PostgreSQL 数据库
  --postgres-host HOST        外部 PostgreSQL 主机
  --postgres-user USER        外部 PostgreSQL 用户
  --postgres-password PASS    外部 PostgreSQL 密码
  --dry-run                   只显示将要执行的命令
  --skip-deps                 跳过依赖服务部署
  --config-file FILE          自定义配置文件路径

示例:
  $0 --domain example.com --cloudflare-token cf-token --email <EMAIL>
  $0 --domain example.com --cloudflare-token cf-token --email <EMAIL> --postgres-external --postgres-host db.example.com

EOF
}

# 参数解析
DOMAIN=""
CLOUDFLARE_TOKEN=""
EMAIL=""
INTERNAL_PORT="8443"
POSTGRES_EXTERNAL=false
POSTGRES_HOST=""
POSTGRES_USER=""
POSTGRES_PASSWORD=""
DRY_RUN=false
SKIP_DEPS=false
CONFIG_FILE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --domain)
            DOMAIN="$2"
            shift 2
            ;;
        --cloudflare-token)
            CLOUDFLARE_TOKEN="$2"
            shift 2
            ;;
        --email)
            EMAIL="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -r|--release)
            RELEASE_NAME="$2"
            shift 2
            ;;
        --internal-port)
            INTERNAL_PORT="$2"
            shift 2
            ;;
        --postgres-external)
            POSTGRES_EXTERNAL=true
            shift
            ;;
        --postgres-host)
            POSTGRES_HOST="$2"
            shift 2
            ;;
        --postgres-user)
            POSTGRES_USER="$2"
            shift 2
            ;;
        --postgres-password)
            POSTGRES_PASSWORD="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --skip-deps)
            SKIP_DEPS=true
            shift
            ;;
        --config-file)
            CONFIG_FILE="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证必需参数
if [[ -z "$DOMAIN" || -z "$CLOUDFLARE_TOKEN" || -z "$EMAIL" ]]; then
    log_error "缺少必需参数"
    show_help
    exit 1
fi

# 如果使用外部数据库，验证相关参数
if [[ "$POSTGRES_EXTERNAL" == "true" ]]; then
    if [[ -z "$POSTGRES_HOST" || -z "$POSTGRES_USER" || -z "$POSTGRES_PASSWORD" ]]; then
        log_error "使用外部数据库时必须提供 --postgres-host, --postgres-user, --postgres-password"
        exit 1
    fi
fi

# 执行命令函数
execute_command() {
    local cmd="$1"
    if [[ "$DRY_RUN" == "true" ]]; then
        echo "[DRY-RUN] $cmd"
    else
        log_info "执行: $cmd"
        eval "$cmd"
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    local missing_deps=()
    
    if ! command -v kubectl &> /dev/null; then
        missing_deps+=("kubectl")
    fi
    
    if ! command -v helm &> /dev/null; then
        missing_deps+=("helm")
    fi
    
    if ! command -v jq &> /dev/null; then
        missing_deps+=("jq")
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "缺少依赖: ${missing_deps[*]}"
        exit 1
    fi
    
    # 检查 Kubernetes 连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到 Kubernetes 集群"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 环境检查
check_environment() {
    log_info "检查部署环境..."
    
    # 检查命名空间是否存在
    if kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_warning "命名空间 $NAMESPACE 已存在"
    fi
    
    # 检查是否已有同名的 Helm release
    if helm list -n "$NAMESPACE" | grep -q "$RELEASE_NAME"; then
        log_warning "Helm release $RELEASE_NAME 已存在，将进行升级"
    fi
    
    # 检查 cert-manager 是否安装
    if ! kubectl get crd certificates.cert-manager.io &> /dev/null; then
        log_error "cert-manager 未安装，请先安装 cert-manager"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 创建配置目录
create_config_directories() {
    log_info "创建配置目录..."
    
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$BACKUP_DIR"
    
    log_success "配置目录创建完成"
}

# 生成配置文件
generate_config_file() {
    log_info "生成配置文件..."
    
    local config_file="${CONFIG_FILE:-${CONFIG_DIR}/internal-server-${DOMAIN}.yaml}"
    
    cat > "$config_file" << EOF
# 内部服务器配置 - 自动生成
# 域名: $DOMAIN
# 生成时间: $(date -u +"%Y-%m-%dT%H:%M:%SZ")

serverName: $DOMAIN

certManager:
  clusterIssuer: cloudflare-letsencrypt

ingress:
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: cloudflare-letsencrypt
  tlsEnabled: true

# 启用所有核心服务
initSecrets:
  enabled: true

deploymentMarkers:
  enabled: true

synapse:
  enabled: true
  ingress:
    host: matrix.$DOMAIN
    annotations:
      kubernetes.io/ingress.class: nginx
      cert-manager.io/cluster-issuer: cloudflare-letsencrypt
      nginx.ingress.kubernetes.io/proxy-body-size: 100M
    tlsEnabled: true
  
  media:
    storage:
      size: 50Gi
      storageClass: fast-ssd
  
  workers:
    client-reader:
      enabled: true
      replicas: 2
    federation-inbound:
      enabled: true
      replicas: 1

matrixAuthenticationService:
  enabled: true
  ingress:
    host: mas.$DOMAIN
    annotations:
      kubernetes.io/ingress.class: nginx
      cert-manager.io/cluster-issuer: cloudflare-letsencrypt
    tlsEnabled: true

matrixRTC:
  enabled: true
  ingress:
    host: rtc.$DOMAIN
    annotations:
      kubernetes.io/ingress.class: nginx
      cert-manager.io/cluster-issuer: cloudflare-letsencrypt
    tlsEnabled: true
  
  sfu:
    enabled: true
    exposedServices:
      rtcTcp:
        enabled: true
        portType: NodePort
        port: 30881
      rtcMuxedUdp:
        enabled: true
        portType: NodePort
        port: 30882

elementWeb:
  enabled: true
  ingress:
    host: element.$DOMAIN
    annotations:
      kubernetes.io/ingress.class: nginx
      cert-manager.io/cluster-issuer: cloudflare-letsencrypt
    tlsEnabled: true

EOF

    # 添加数据库配置
    if [[ "$POSTGRES_EXTERNAL" == "true" ]]; then
        cat >> "$config_file" << EOF
postgres:
  enabled: false

synapse:
  postgres:
    host: $POSTGRES_HOST
    port: 5432
    user: $POSTGRES_USER
    database: synapse
    password:
      secret: postgres-credentials
      secretKey: synapse-password

matrixAuthenticationService:
  postgres:
    host: $POSTGRES_HOST
    port: 5432
    user: $POSTGRES_USER
    database: mas
    password:
      secret: postgres-credentials
      secretKey: mas-password
EOF
    else
        cat >> "$config_file" << EOF
postgres:
  enabled: true
  storage:
    size: 20Gi
    storageClass: fast-ssd
EOF
    fi

    # 添加 HAProxy 配置
    cat >> "$config_file" << EOF

haproxy:
  enabled: true

wellKnownDelegation:
  enabled: false

# 资源配置
resources:
  synapse:
    requests:
      memory: 2Gi
      cpu: 1000m
    limits:
      memory: 4Gi
      cpu: 2000m
  
  matrixAuthenticationService:
    requests:
      memory: 512Mi
      cpu: 500m
    limits:
      memory: 1Gi
      cpu: 1000m
  
  postgres:
    requests:
      memory: 1Gi
      cpu: 500m
    limits:
      memory: 2Gi
      cpu: 1000m
EOF

    echo "$config_file"
}

# 创建命名空间
create_namespace() {
    log_info "创建命名空间: $NAMESPACE"
    execute_command "kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -"
}

# 创建敏感信息 Secrets
create_secrets() {
    log_info "创建敏感信息 Secrets..."

    # Cloudflare API Token
    execute_command "kubectl create secret generic cloudflare-api-token \\
        --from-literal=api-token='$CLOUDFLARE_TOKEN' \\
        -n $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -"

    # 外部数据库密码
    if [[ "$POSTGRES_EXTERNAL" == "true" ]]; then
        execute_command "kubectl create secret generic postgres-credentials \\
            --from-literal=synapse-password='$POSTGRES_PASSWORD' \\
            --from-literal=mas-password='$POSTGRES_PASSWORD' \\
            -n $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -"
    fi

    log_success "Secrets 创建完成"
}

# 部署依赖服务
deploy_dependencies() {
    if [[ "$SKIP_DEPS" == "true" ]]; then
        log_info "跳过依赖服务部署"
        return
    fi

    log_info "部署依赖服务..."

    # 部署 ClusterIssuer
    deploy_cluster_issuer

    # 部署 Ingress Controller（如果需要）
    deploy_ingress_controller

    log_success "依赖服务部署完成"
}

# 部署 ClusterIssuer
deploy_cluster_issuer() {
    log_info "部署 Cloudflare ClusterIssuer..."

    local issuer_yaml="apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: cloudflare-letsencrypt
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: $EMAIL
    privateKeySecretRef:
      name: cloudflare-letsencrypt
    solvers:
    - dns01:
        cloudflare:
          apiTokenSecretRef:
            name: cloudflare-api-token
            key: api-token"

    if [[ "$DRY_RUN" == "true" ]]; then
        echo "[DRY-RUN] kubectl apply -f - <<< '$issuer_yaml'"
    else
        echo "$issuer_yaml" | kubectl apply -f -
    fi
}

# 检查 Ingress Controller
deploy_ingress_controller() {
    if kubectl get ingressclass nginx &> /dev/null; then
        log_info "Nginx Ingress Controller 已存在"
        return
    fi

    log_warning "未检测到 Nginx Ingress Controller，建议手动安装"
    log_info "安装命令: helm upgrade --install ingress-nginx ingress-nginx/ingress-nginx --namespace ingress-nginx --create-namespace"
}

# 部署 Matrix 服务
deploy_matrix_services() {
    log_info "部署 Matrix 服务..."

    local config_file="${CONFIG_FILE:-${CONFIG_DIR}/internal-server-${DOMAIN}.yaml}"

    execute_command "helm upgrade --install $RELEASE_NAME ./charts/matrix-stack \\
        -f '$config_file' \\
        --namespace $NAMESPACE \\
        --create-namespace \\
        --wait \\
        --timeout 20m"

    log_success "Matrix 服务部署完成"
}

# 主函数入口
main() {
    log_info "开始内部服务器自动化部署"
    log_info "域名: $DOMAIN"
    log_info "命名空间: $NAMESPACE"
    log_info "Release: $RELEASE_NAME"

    check_dependencies
    check_environment
    create_config_directories

    local config_file
    config_file=$(generate_config_file)
    log_success "配置文件生成: $config_file"

    create_namespace
    create_secrets
    deploy_dependencies
    deploy_matrix_services

    log_success "内部服务器部署完成！"
    log_info "请运行健康检查脚本验证部署状态"
}

# 运行主函数
main "$@"
