#!/bin/bash

# RouterOS API 动态IP检测使用示例
# Copyright 2025 New Vector Ltd
# SPDX-License-Identifier: AGPL-3.0-only

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "=========================================="
echo "RouterOS API 动态IP检测使用示例"
echo "=========================================="
echo

# 示例配置
EXAMPLE_DOMAIN="example.com"
EXAMPLE_ROUTEROS_HOST="***********"
EXAMPLE_ROUTEROS_USERNAME="admin"
EXAMPLE_ROUTEROS_PASSWORD="your-password"

log_info "本示例将演示如何使用RouterOS API进行动态IP检测"
echo

# 1. 基本配置示例
log_info "1. 基本配置示例"
echo "export ROUTEROS_HOST=\"$EXAMPLE_ROUTEROS_HOST\""
echo "export ROUTEROS_USERNAME=\"$EXAMPLE_ROUTEROS_USERNAME\""
echo "export ROUTEROS_PASSWORD=\"$EXAMPLE_ROUTEROS_PASSWORD\""
echo "export DOMAIN=\"$EXAMPLE_DOMAIN\""
echo

# 2. 单次IP检测示例
log_info "2. 单次IP检测示例"
echo "./scripts/dynamic-ip-manager.sh detect \\"
echo "  --domain $EXAMPLE_DOMAIN \\"
echo "  --routeros-host $EXAMPLE_ROUTEROS_HOST \\"
echo "  --routeros-username $EXAMPLE_ROUTEROS_USERNAME \\"
echo "  --routeros-password $EXAMPLE_ROUTEROS_PASSWORD"
echo

# 3. 使用环境变量的示例
log_info "3. 使用环境变量的示例"
cat << 'EOF'
# 设置环境变量
export ROUTEROS_HOST="***********"
export ROUTEROS_USERNAME="admin"
export ROUTEROS_PASSWORD="your-password"
export DOMAIN="example.com"

# 执行检测
./scripts/dynamic-ip-manager.sh detect --domain $DOMAIN
EOF
echo

# 4. 持续监控示例
log_info "4. 持续监控示例"
echo "./scripts/dynamic-ip-manager.sh monitor \\"
echo "  --domain $EXAMPLE_DOMAIN \\"
echo "  --routeros-host $EXAMPLE_ROUTEROS_HOST \\"
echo "  --check-interval 5"
echo

# 5. 高级配置示例
log_info "5. 高级配置示例"
cat << 'EOF'
# 完整配置
export ROUTEROS_HOST="***********"
export ROUTEROS_USERNAME="api-user"
export ROUTEROS_PASSWORD="secure-password"
export ROUTEROS_PORT="443"
export ROUTEROS_USE_HTTPS="true"
export ROUTEROS_WAN_INTERFACE="ether1"
export ROUTEROS_TIMEOUT="10"
export ROUTEROS_RETRY_COUNT="3"
export ROUTEROS_RETRY_DELAY="2"
export DOMAIN="example.com"
export CHECK_INTERVAL="5"

# 执行监控
./scripts/dynamic-ip-manager.sh monitor --domain $DOMAIN
EOF
echo

# 6. 测试连接示例
log_info "6. 测试RouterOS连接"
echo "./scripts/test-routeros-api.sh \\"
echo "  --routeros-host $EXAMPLE_ROUTEROS_HOST \\"
echo "  --routeros-username $EXAMPLE_ROUTEROS_USERNAME \\"
echo "  --routeros-password $EXAMPLE_ROUTEROS_PASSWORD \\"
echo "  --domain $EXAMPLE_DOMAIN"
echo

# 7. Kubernetes部署示例
log_info "7. Kubernetes部署示例"
cat << 'EOF'
# 1. 修改配置文件
cp scripts/routeros-config-example.yaml routeros-config.yaml
# 编辑 routeros-config.yaml，设置正确的RouterOS主机和密码

# 2. 应用配置
kubectl apply -f routeros-config.yaml

# 3. 检查部署状态
kubectl get pods -n matrix-external
kubectl logs -f deployment/routeros-dynamic-ip-manager -n matrix-external
EOF
echo

# 8. systemd服务示例
log_info "8. systemd服务部署示例"
cat << 'EOF'
# 1. 创建服务文件
sudo tee /etc/systemd/system/routeros-dynamic-ip.service << 'EOL'
[Unit]
Description=RouterOS Dynamic IP Manager
After=network.target

[Service]
Type=simple
User=nobody
Environment=ROUTEROS_HOST=***********
Environment=ROUTEROS_USERNAME=admin
Environment=ROUTEROS_PASSWORD=your-password
Environment=DOMAIN=example.com
Environment=CHECK_INTERVAL=5
ExecStart=/opt/scripts/dynamic-ip-manager.sh monitor --domain example.com
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOL

# 2. 启动服务
sudo systemctl daemon-reload
sudo systemctl enable routeros-dynamic-ip
sudo systemctl start routeros-dynamic-ip

# 3. 查看状态
sudo systemctl status routeros-dynamic-ip
sudo journalctl -u routeros-dynamic-ip -f
EOF
echo

# 9. 故障排除示例
log_info "9. 故障排除示例"
cat << 'EOF'
# 测试网络连接
ping ***********

# 测试RouterOS API
curl -k -u "admin:password" "https://***********:443/rest/system/identity"

# 查看详细日志
export LOG_LEVEL="debug"
./scripts/dynamic-ip-manager.sh detect --domain example.com 2>&1 | tee debug.log

# 运行连接测试
./scripts/test-routeros-api.sh --routeros-host ***********
EOF
echo

# 10. 安全配置示例
log_info "10. 安全配置建议"
cat << 'EOF'
# RouterOS安全配置
# 1. 创建专用API用户
/user add name=api-user password=secure-random-password group=read

# 2. 限制API访问IP
/ip service set api-ssl address=***********00/32

# 3. 启用HTTPS
/ip service set api-ssl certificate=your-certificate

# 4. 定期更新密码
/user set api-user password=new-secure-password
EOF
echo

log_success "示例演示完成！"
log_info "请根据您的实际环境修改配置参数"
log_info "详细文档请参考: RouterOS-API-部署指南.md"
echo

# 交互式配置生成器
read -p "是否要生成自定义配置？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo
    log_info "配置生成器"
    
    read -p "请输入RouterOS设备IP地址: " user_host
    read -p "请输入RouterOS用户名 [admin]: " user_username
    user_username=${user_username:-admin}
    read -s -p "请输入RouterOS密码: " user_password
    echo
    read -p "请输入域名: " user_domain
    read -p "请输入检测间隔（秒） [5]: " user_interval
    user_interval=${user_interval:-5}
    
    echo
    log_success "生成的配置："
    echo "export ROUTEROS_HOST=\"$user_host\""
    echo "export ROUTEROS_USERNAME=\"$user_username\""
    echo "export ROUTEROS_PASSWORD=\"$user_password\""
    echo "export DOMAIN=\"$user_domain\""
    echo "export CHECK_INTERVAL=\"$user_interval\""
    echo
    echo "测试命令："
    echo "./scripts/dynamic-ip-manager.sh detect --domain $user_domain"
    echo
fi
