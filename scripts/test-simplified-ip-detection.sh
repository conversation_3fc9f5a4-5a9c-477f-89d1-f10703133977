#!/bin/bash

# Copyright 2025 New Vector Ltd
# SPDX-License-Identifier: AGPL-3.0-only

# 简化IP检测方案测试脚本

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 测试配置
TEST_DOMAIN="${1:-example.com}"
SCRIPT_PATH="$(dirname "$0")/dynamic-ip-manager.sh"

# 显示帮助信息
show_help() {
    cat << EOF
简化IP检测方案测试脚本

用法: $0 [域名] [选项]

参数:
  域名    测试用的域名 (默认: example.com)

选项:
  -h, --help              显示此帮助信息
  --test-interfaces       测试所有网络接口
  --test-services         测试所有IP检测服务
  --performance-test      执行性能测试
  --all                   执行所有测试

测试项目:
1. 基本IP检测功能
2. 网络接口检测
3. IP检测服务测试
4. 性能和稳定性测试
5. 错误处理测试

EOF
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖工具..."
    
    local missing_tools=()
    
    for tool in curl ping ip; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "缺少必需工具: ${missing_tools[*]}"
        return 1
    fi
    
    log_success "依赖检查通过"
}

# 测试基本IP检测功能
test_basic_detection() {
    log_info "=== 测试基本IP检测功能 ==="
    
    local start_time=$(date +%s)
    
    if detected_ip=$(bash "$SCRIPT_PATH" detect --domain "$TEST_DOMAIN" 2>/dev/null); then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        log_success "基本IP检测成功: $detected_ip"
        log_info "检测耗时: ${duration}秒"
        
        # 验证IP格式
        if [[ "$detected_ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            log_success "IP格式验证通过"
            return 0
        else
            log_error "IP格式验证失败: $detected_ip"
            return 1
        fi
    else
        log_error "基本IP检测失败"
        return 1
    fi
}

# 测试网络接口检测
test_interface_detection() {
    log_info "=== 测试网络接口检测 ==="
    
    # 获取可用的网络接口
    local interfaces
    interfaces=$(ip route | grep default | awk '{print $5}' | sort -u)
    
    if [[ -z "$interfaces" ]]; then
        log_warning "未找到可用的网络接口"
        return 1
    fi
    
    local success_count=0
    local total_count=0
    
    for interface in $interfaces; do
        ((total_count++))
        log_info "测试网络接口: $interface"
        
        if result=$(bash "$SCRIPT_PATH" detect --domain "$TEST_DOMAIN" --preferred-interface "$interface" 2>/dev/null); then
            ((success_count++))
            log_success "接口 $interface 测试成功: $result"
        else
            log_warning "接口 $interface 测试失败"
        fi
    done
    
    log_info "网络接口测试结果: $success_count/$total_count 成功"
    
    if [[ $success_count -gt 0 ]]; then
        return 0
    else
        return 1
    fi
}

# 测试IP检测服务
test_ip_services() {
    log_info "=== 测试IP检测服务 ==="
    
    local services=(
        "https://ping0.cc"
        "https://ipv4.icanhazip.com"
        "https://ifconfig.info"
    )
    
    local success_count=0
    local total_count=${#services[@]}
    
    for service in "${services[@]}"; do
        log_info "测试服务: $service"
        
        local detected_ip
        detected_ip=$(timeout 10 curl -s --max-time 5 "$service" 2>/dev/null | grep -oE '^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$' | head -1)
        
        if [[ -n "$detected_ip" ]]; then
            ((success_count++))
            log_success "服务 $service 可用: $detected_ip"
        else
            log_warning "服务 $service 不可用"
        fi
    done
    
    log_info "IP检测服务测试结果: $success_count/$total_count 可用"
    
    if [[ $success_count -gt 0 ]]; then
        return 0
    else
        return 1
    fi
}

# 性能测试
test_performance() {
    log_info "=== 性能测试 ==="
    
    local test_count=5
    local success_count=0
    local total_time=0
    local results=()
    
    for i in $(seq 1 $test_count); do
        log_info "第 $i 次性能测试..."
        local start_time=$(date +%s.%N)
        
        if result=$(bash "$SCRIPT_PATH" detect --domain "$TEST_DOMAIN" 2>/dev/null); then
            local end_time=$(date +%s.%N)
            local duration=$(echo "$end_time - $start_time" | bc -l 2>/dev/null || echo "0")
            total_time=$(echo "$total_time + $duration" | bc -l 2>/dev/null || echo "$total_time")
            ((success_count++))
            results+=("$result")
            
            log_success "第 $i 次测试成功: $result (${duration}秒)"
        else
            log_error "第 $i 次测试失败"
        fi
        
        sleep 1
    done
    
    if [[ $success_count -gt 0 ]]; then
        local avg_time
        if command -v bc >/dev/null 2>&1; then
            avg_time=$(echo "scale=3; $total_time / $success_count" | bc -l)
        else
            avg_time="N/A"
        fi
        
        log_success "性能测试完成:"
        log_info "  成功率: $success_count/$test_count"
        log_info "  平均耗时: ${avg_time}秒"
        
        # 检查结果一致性
        local unique_ips
        unique_ips=$(printf '%s\n' "${results[@]}" | sort -u | wc -l)
        if [[ $unique_ips -eq 1 ]]; then
            log_success "  结果一致性: 优秀 (所有结果相同)"
        elif [[ $unique_ips -le 2 ]]; then
            log_warning "  结果一致性: 良好 (检测到 $unique_ips 个不同IP)"
        else
            log_error "  结果一致性: 差 (检测到 $unique_ips 个不同IP)"
        fi
        
        return 0
    else
        log_error "所有性能测试都失败"
        return 1
    fi
}

# 错误处理测试
test_error_handling() {
    log_info "=== 错误处理测试 ==="
    
    # 测试无效域名
    log_info "测试无效域名处理..."
    if bash "$SCRIPT_PATH" detect --domain "invalid-domain-12345.nonexistent" >/dev/null 2>&1; then
        log_warning "无效域名测试: 应该失败但成功了"
    else
        log_success "无效域名测试: 正确处理了无效域名"
    fi
    
    # 测试无效接口
    log_info "测试无效网络接口处理..."
    if bash "$SCRIPT_PATH" detect --domain "$TEST_DOMAIN" --preferred-interface "nonexistent999" >/dev/null 2>&1; then
        log_success "无效接口测试: 脚本处理了无效接口并使用了备用方法"
    else
        log_warning "无效接口测试: 脚本完全失败"
    fi
    
    # 测试缺少参数
    log_info "测试缺少参数处理..."
    if bash "$SCRIPT_PATH" detect >/dev/null 2>&1; then
        log_warning "缺少参数测试: 应该失败但成功了"
    else
        log_success "缺少参数测试: 正确处理了缺少参数的情况"
    fi
    
    return 0
}

# 网络环境信息
show_network_info() {
    log_info "=== 网络环境信息 ==="
    
    log_info "网络接口:"
    ip addr show | grep -E "^[0-9]+:" | while read -r line; do
        local interface=$(echo "$line" | awk '{print $2}' | sed 's/://')
        local status=$(echo "$line" | grep -o "state [A-Z]*" | awk '{print $2}')
        log_info "  $interface: $status"
    done
    
    log_info "默认路由:"
    local default_route
    default_route=$(ip route get ******* 2>/dev/null || echo "无法获取")
    log_info "  $default_route"
    
    log_info "DNS配置:"
    if [[ -f /etc/resolv.conf ]]; then
        grep "nameserver" /etc/resolv.conf | head -3 | while read -r line; do
            log_info "  $line"
        done
    else
        log_info "  无法读取DNS配置"
    fi
    
    log_info "代理设置:"
    local proxy_found=false
    for proxy_var in http_proxy https_proxy HTTP_PROXY HTTPS_PROXY; do
        if [[ -n "${!proxy_var:-}" ]]; then
            log_info "  $proxy_var=${!proxy_var}"
            proxy_found=true
        fi
    done
    if [[ "$proxy_found" == "false" ]]; then
        log_info "  未检测到代理配置"
    fi
}

# 主函数
main() {
    case "${1:-}" in
        -h|--help)
            show_help
            exit 0
            ;;
    esac
    
    log_info "开始简化IP检测方案测试..."
    log_info "测试域名: $TEST_DOMAIN"
    
    # 检查依赖
    if ! check_dependencies; then
        exit 1
    fi
    
    # 检查脚本文件
    if [[ ! -f "$SCRIPT_PATH" ]]; then
        log_error "找不到脚本文件: $SCRIPT_PATH"
        exit 1
    fi
    
    # 显示网络环境信息
    show_network_info
    echo
    
    local failed_tests=0
    
    # 执行测试
    test_basic_detection || ((failed_tests++))
    echo
    test_interface_detection || ((failed_tests++))
    echo
    test_ip_services || ((failed_tests++))
    echo
    test_performance || ((failed_tests++))
    echo
    test_error_handling || ((failed_tests++))
    echo
    
    # 总结
    log_info "=== 测试总结 ==="
    if [[ $failed_tests -eq 0 ]]; then
        log_success "所有测试通过！简化IP检测方案工作正常。"
        log_info "建议："
        log_info "1. 在生产环境中监控IP检测的成功率和响应时间"
        log_info "2. 根据网络环境调整超时设置"
        log_info "3. 定期测试各个IP检测服务的可用性"
        exit 0
    else
        log_error "有 $failed_tests 个测试失败，请检查相关功能。"
        log_info "故障排除建议："
        log_info "1. 检查网络连接: ping *******"
        log_info "2. 检查DNS解析: nslookup ping0.cc"
        log_info "3. 检查防火墙设置"
        log_info "4. 检查代理配置"
        exit 1
    fi
}

# 运行主函数
main "$@"
