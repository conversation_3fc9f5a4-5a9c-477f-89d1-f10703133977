#!/bin/bash

# Copyright 2025 New Vector Ltd
# SPDX-License-Identifier: AGPL-3.0-only

# 动态IP检测功能测试脚本

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 测试配置
TEST_DOMAIN="${1:-example.com}"
SCRIPT_PATH="$(dirname "$0")/dynamic-ip-manager.sh"

# 显示帮助信息
show_help() {
    cat << EOF
动态IP检测功能测试脚本

用法: $0 [域名]

参数:
  域名    测试用的域名 (默认: example.com)

示例:
  $0 example.com

测试项目:
1. IP检测功能测试
2. DNS服务器故障转移测试
3. 性能测试 (10秒间隔)
4. 稳定性测试
5. 错误处理测试

EOF
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖工具..."
    
    local missing_tools=()
    
    for tool in dig ping curl jq; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "缺少必需工具: ${missing_tools[*]}"
        log_error "请安装缺少的工具后重试"
        return 1
    fi
    
    log_success "所有依赖工具检查通过"
}

# 测试1：基本IP检测功能
test_basic_detection() {
    log_info "=== 测试1：基本IP检测功能 ==="
    
    if [[ ! -f "$SCRIPT_PATH" ]]; then
        log_error "找不到脚本文件: $SCRIPT_PATH"
        return 1
    fi
    
    log_info "执行IP检测..."
    local start_time=$(date +%s)
    
    if detected_ip=$(bash "$SCRIPT_PATH" detect --domain "$TEST_DOMAIN" 2>/dev/null); then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        log_success "IP检测成功: $detected_ip"
        log_info "检测耗时: ${duration}秒"
        
        # 验证IP格式
        if [[ "$detected_ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            log_success "IP格式验证通过"
        else
            log_error "IP格式验证失败: $detected_ip"
            return 1
        fi
    else
        log_error "IP检测失败"
        return 1
    fi
}

# 测试2：DNS服务器故障转移
test_dns_failover() {
    log_info "=== 测试2：DNS服务器故障转移测试 ==="
    
    # 测试各个DNS服务器的可达性
    local dns_servers=("*******" "*******" "************")
    local dns_names=("Cloudflare" "Google" "腾讯DNSPod")
    
    for i in "${!dns_servers[@]}"; do
        local dns_server="${dns_servers[$i]}"
        local dns_name="${dns_names[$i]}"
        
        log_info "测试 ${dns_name} DNS (${dns_server})..."
        
        if ping -c 1 -W 3 "$dns_server" >/dev/null 2>&1; then
            log_success "${dns_name} DNS 可达"
            
            # 测试DNS解析
            if dig +short +time=5 google.com @"$dns_server" >/dev/null 2>&1; then
                log_success "${dns_name} DNS 解析功能正常"
            else
                log_warning "${dns_name} DNS 解析功能异常"
            fi
        else
            log_warning "${dns_name} DNS 不可达"
        fi
    done
}

# 测试3：性能测试 (10秒间隔)
test_performance() {
    log_info "=== 测试3：性能测试 (10秒间隔，共5次) ==="
    
    local total_time=0
    local success_count=0
    local test_count=5
    
    for i in $(seq 1 $test_count); do
        log_info "第 $i 次检测..."
        local start_time=$(date +%s.%N)
        
        if bash "$SCRIPT_PATH" detect --domain "$TEST_DOMAIN" >/dev/null 2>&1; then
            local end_time=$(date +%s.%N)
            local duration=$(echo "$end_time - $start_time" | bc -l)
            total_time=$(echo "$total_time + $duration" | bc -l)
            ((success_count++))
            
            log_success "第 $i 次检测成功，耗时: ${duration}秒"
        else
            log_error "第 $i 次检测失败"
        fi
        
        # 等待10秒（除了最后一次）
        if [[ $i -lt $test_count ]]; then
            sleep 10
        fi
    done
    
    if [[ $success_count -gt 0 ]]; then
        local avg_time=$(echo "scale=3; $total_time / $success_count" | bc -l)
        log_success "性能测试完成："
        log_info "  成功率: $success_count/$test_count ($(echo "scale=1; $success_count * 100 / $test_count" | bc -l)%)"
        log_info "  平均耗时: ${avg_time}秒"
        
        # 性能评估
        if (( $(echo "$avg_time < 5" | bc -l) )); then
            log_success "性能评估: 优秀 (< 5秒)"
        elif (( $(echo "$avg_time < 10" | bc -l) )); then
            log_success "性能评估: 良好 (< 10秒)"
        else
            log_warning "性能评估: 需要优化 (>= 10秒)"
        fi
    else
        log_error "所有性能测试都失败"
        return 1
    fi
}

# 测试4：稳定性测试
test_stability() {
    log_info "=== 测试4：稳定性测试 (连续10次快速检测) ==="
    
    local results=()
    local unique_ips=()
    
    for i in $(seq 1 10); do
        if detected_ip=$(bash "$SCRIPT_PATH" detect --domain "$TEST_DOMAIN" 2>/dev/null); then
            results+=("$detected_ip")
            
            # 检查是否是新的IP
            if [[ ! " ${unique_ips[*]} " =~ " ${detected_ip} " ]]; then
                unique_ips+=("$detected_ip")
            fi
            
            log_info "第 $i 次: $detected_ip"
        else
            log_error "第 $i 次检测失败"
            results+=("FAILED")
        fi
        
        sleep 1
    done
    
    log_info "稳定性测试结果："
    log_info "  总检测次数: 10"
    log_info "  成功次数: $(echo "${results[@]}" | grep -v FAILED | wc -w)"
    log_info "  唯一IP数量: ${#unique_ips[@]}"
    log_info "  检测到的IP: ${unique_ips[*]}"
    
    if [[ ${#unique_ips[@]} -eq 1 ]]; then
        log_success "稳定性测试通过：所有检测结果一致"
    elif [[ ${#unique_ips[@]} -le 2 ]]; then
        log_warning "稳定性测试警告：检测到多个IP，可能存在网络切换"
    else
        log_error "稳定性测试失败：检测结果不稳定"
        return 1
    fi
}

# 测试5：错误处理测试
test_error_handling() {
    log_info "=== 测试5：错误处理测试 ==="
    
    # 测试无效域名
    log_info "测试无效域名处理..."
    if bash "$SCRIPT_PATH" detect --domain "invalid-domain-that-does-not-exist.com" >/dev/null 2>&1; then
        log_warning "无效域名测试：脚本应该失败但却成功了"
    else
        log_success "无效域名测试：正确处理了无效域名"
    fi
    
    # 测试缺少参数
    log_info "测试缺少参数处理..."
    if bash "$SCRIPT_PATH" detect >/dev/null 2>&1; then
        log_warning "缺少参数测试：脚本应该失败但却成功了"
    else
        log_success "缺少参数测试：正确处理了缺少参数的情况"
    fi
}

# 生成测试报告
generate_report() {
    local report_file="ip-detection-test-report-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$report_file" << EOF
动态IP检测功能测试报告
===================

测试时间: $(date)
测试域名: $TEST_DOMAIN
脚本路径: $SCRIPT_PATH

测试结果:
1. 基本IP检测功能: $(test_basic_detection >/dev/null 2>&1 && echo "通过" || echo "失败")
2. DNS服务器故障转移: $(test_dns_failover >/dev/null 2>&1 && echo "通过" || echo "失败")
3. 性能测试: $(test_performance >/dev/null 2>&1 && echo "通过" || echo "失败")
4. 稳定性测试: $(test_stability >/dev/null 2>&1 && echo "通过" || echo "失败")
5. 错误处理测试: $(test_error_handling >/dev/null 2>&1 && echo "通过" || echo "失败")

系统信息:
- 操作系统: $(uname -s)
- 内核版本: $(uname -r)
- 网络接口: $(ip route get ******* | grep -oP 'dev \K\S+' | head -1)

建议:
- 如果性能测试失败，考虑优化网络连接或DNS配置
- 如果稳定性测试失败，检查网络环境是否稳定
- 定期运行此测试以确保功能正常

EOF

    log_success "测试报告已生成: $report_file"
}

# 主函数
main() {
    if [[ "${1:-}" == "-h" || "${1:-}" == "--help" ]]; then
        show_help
        exit 0
    fi
    
    log_info "开始动态IP检测功能测试..."
    log_info "测试域名: $TEST_DOMAIN"
    
    # 检查依赖
    if ! check_dependencies; then
        exit 1
    fi
    
    local failed_tests=0
    
    # 执行所有测试
    test_basic_detection || ((failed_tests++))
    echo
    test_dns_failover || ((failed_tests++))
    echo
    test_performance || ((failed_tests++))
    echo
    test_stability || ((failed_tests++))
    echo
    test_error_handling || ((failed_tests++))
    echo
    
    # 生成报告
    generate_report
    
    # 总结
    log_info "=== 测试总结 ==="
    if [[ $failed_tests -eq 0 ]]; then
        log_success "所有测试通过！动态IP检测功能工作正常。"
        exit 0
    else
        log_error "有 $failed_tests 个测试失败，请检查相关功能。"
        exit 1
    fi
}

# 运行主函数
main "$@"
