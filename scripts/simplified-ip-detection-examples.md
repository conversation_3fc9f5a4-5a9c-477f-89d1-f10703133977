# 简化IP检测方案使用示例

## 📋 概述

本文档提供了在不同网络环境中使用简化IP检测方案的具体示例和配置建议。

## 🌐 网络环境分类

### 1. 标准网络环境
**特征**: 直连互联网，无代理，无旁路网关

```bash
# 基本使用
./scripts/dynamic-ip-manager.sh detect --domain example.com

# 监控模式
./scripts/dynamic-ip-manager.sh monitor --domain example.com --check-interval 10

# 环境变量配置
export NETWORK_TIMEOUT=5
export ENABLE_INTERFACE_DETECTION=true
```

### 2. 旁路网关环境
**特征**: 存在旁路网关，需要指定特定网络接口

```bash
# 指定首选网络接口
./scripts/dynamic-ip-manager.sh detect --domain example.com --preferred-interface eth0

# 多接口环境
./scripts/dynamic-ip-manager.sh detect --domain example.com --preferred-interface ens3

# 禁用自动接口检测（仅使用指定接口）
./scripts/dynamic-ip-manager.sh detect --domain example.com \
  --preferred-interface eth0 \
  --disable-interface-detect

# 环境变量配置
export PREFERRED_INTERFACE=eth0
export NETWORK_TIMEOUT=10
```

### 3. 企业网络环境
**特征**: 有防火墙限制，可能有代理

```bash
# 增加超时时间
./scripts/dynamic-ip-manager.sh detect --domain example.com --network-timeout 15

# 企业环境监控（降低检测频率）
./scripts/dynamic-ip-manager.sh monitor --domain example.com --check-interval 60

# 环境变量配置
export NETWORK_TIMEOUT=15
export CHECK_INTERVAL=60
```

### 4. 移动网络环境
**特征**: 网络不稳定，IP频繁变化

```bash
# 高频检测
./scripts/dynamic-ip-manager.sh monitor --domain example.com --check-interval 30

# 增加超时容错
./scripts/dynamic-ip-manager.sh detect --domain example.com --network-timeout 20

# 环境变量配置
export NETWORK_TIMEOUT=20
export CHECK_INTERVAL=30
```

### 5. 云环境NAT
**特征**: 多层NAT，复杂的网络拓扑

```bash
# 标准检测（通常云环境路由配置良好）
./scripts/dynamic-ip-manager.sh detect --domain example.com

# 如果有多个网络接口，指定主要接口
./scripts/dynamic-ip-manager.sh detect --domain example.com --preferred-interface eth0

# 环境变量配置
export NETWORK_TIMEOUT=10
export CHECK_INTERVAL=15
```

## 🔧 Kubernetes部署示例

### 1. 标准部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dynamic-ip-manager
  namespace: matrix-external
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dynamic-ip-manager
  template:
    metadata:
      labels:
        app: dynamic-ip-manager
    spec:
      containers:
      - name: ip-manager
        image: alpine:3.18
        command:
        - /bin/sh
        - -c
        - |
          apk add --no-cache bash curl bind-tools
          exec /scripts/dynamic-ip-manager.sh monitor \
            --domain "${DOMAIN}" \
            --check-interval "${CHECK_INTERVAL}"
        env:
        - name: DOMAIN
          value: "example.com"
        - name: CHECK_INTERVAL
          value: "10"
        - name: NETWORK_TIMEOUT
          value: "10"
        - name: CLOUDFLARE_API_TOKEN
          valueFrom:
            secretKeyRef:
              name: cloudflare-credentials
              key: api-token
        volumeMounts:
        - name: scripts
          mountPath: /scripts
        resources:
          requests:
            memory: "32Mi"
            cpu: "25m"
          limits:
            memory: "64Mi"
            cpu: "50m"
      volumes:
      - name: scripts
        configMap:
          name: dynamic-ip-scripts
          defaultMode: 0755
      hostNetwork: false  # 标准网络模式
```

### 2. 旁路网关环境部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dynamic-ip-manager-bypass
  namespace: matrix-external
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dynamic-ip-manager-bypass
  template:
    metadata:
      labels:
        app: dynamic-ip-manager-bypass
    spec:
      containers:
      - name: ip-manager
        image: alpine:3.18
        command:
        - /bin/sh
        - -c
        - |
          apk add --no-cache bash curl bind-tools iproute2
          exec /scripts/dynamic-ip-manager.sh monitor \
            --domain "${DOMAIN}" \
            --preferred-interface "${PREFERRED_INTERFACE}" \
            --check-interval "${CHECK_INTERVAL}"
        env:
        - name: DOMAIN
          value: "example.com"
        - name: PREFERRED_INTERFACE
          value: "eth0"  # 指定网络接口
        - name: CHECK_INTERVAL
          value: "10"
        - name: NETWORK_TIMEOUT
          value: "15"
        - name: CLOUDFLARE_API_TOKEN
          valueFrom:
            secretKeyRef:
              name: cloudflare-credentials
              key: api-token
        volumeMounts:
        - name: scripts
          mountPath: /scripts
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        securityContext:
          capabilities:
            add:
            - NET_ADMIN  # 需要网络管理权限
      volumes:
      - name: scripts
        configMap:
          name: dynamic-ip-scripts
          defaultMode: 0755
      hostNetwork: true  # 使用主机网络
```

### 3. 企业环境部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dynamic-ip-manager-enterprise
  namespace: matrix-external
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dynamic-ip-manager-enterprise
  template:
    metadata:
      labels:
        app: dynamic-ip-manager-enterprise
    spec:
      containers:
      - name: ip-manager
        image: alpine:3.18
        command:
        - /bin/sh
        - -c
        - |
          apk add --no-cache bash curl bind-tools
          exec /scripts/dynamic-ip-manager.sh monitor \
            --domain "${DOMAIN}" \
            --network-timeout "${NETWORK_TIMEOUT}" \
            --check-interval "${CHECK_INTERVAL}"
        env:
        - name: DOMAIN
          value: "example.com"
        - name: CHECK_INTERVAL
          value: "60"  # 企业环境降低检测频率
        - name: NETWORK_TIMEOUT
          value: "20"  # 增加超时时间
        - name: https_proxy
          value: "http://proxy.company.com:8080"  # 企业代理
        - name: CLOUDFLARE_API_TOKEN
          valueFrom:
            secretKeyRef:
              name: cloudflare-credentials
              key: api-token
        volumeMounts:
        - name: scripts
          mountPath: /scripts
        resources:
          requests:
            memory: "32Mi"
            cpu: "25m"
          limits:
            memory: "64Mi"
            cpu: "50m"
      volumes:
      - name: scripts
        configMap:
          name: dynamic-ip-scripts
          defaultMode: 0755
```

## 🧪 测试和验证

### 1. 基本功能测试
```bash
# 运行简化测试脚本
chmod +x scripts/test-simplified-ip-detection.sh
./scripts/test-simplified-ip-detection.sh example.com

# 手动测试各个组件
curl https://ping0.cc
curl https://ipv4.icanhazip.com
curl https://ifconfig.info
```

### 2. 网络接口测试
```bash
# 查看可用接口
ip addr show

# 测试特定接口
curl --interface eth0 https://ping0.cc
curl --interface wlan0 https://ping0.cc

# 测试脚本接口功能
./scripts/dynamic-ip-manager.sh detect --domain example.com --preferred-interface eth0
```

### 3. 性能测试
```bash
# 测试检测速度
time ./scripts/dynamic-ip-manager.sh detect --domain example.com

# 连续测试稳定性
for i in {1..10}; do
  echo "测试 $i:"
  ./scripts/dynamic-ip-manager.sh detect --domain example.com
  sleep 5
done
```

## 📊 监控配置

### 1. Prometheus监控
```yaml
# prometheus-rules.yaml
groups:
- name: dynamic-ip-detection
  rules:
  - alert: IPDetectionFailure
    expr: increase(ip_detection_failures_total[5m]) > 3
    for: 2m
    annotations:
      summary: "IP检测连续失败"
      description: "动态IP检测在过去5分钟内失败超过3次"
      
  - alert: IPDetectionSlow
    expr: ip_detection_duration_seconds > 15
    for: 1m
    annotations:
      summary: "IP检测响应缓慢"
      description: "IP检测耗时超过15秒"
```

### 2. 日志监控
```bash
# 监控脚本日志
kubectl logs -f deployment/dynamic-ip-manager -n matrix-external

# 过滤错误日志
kubectl logs deployment/dynamic-ip-manager -n matrix-external | grep ERROR

# 监控IP变化
kubectl logs deployment/dynamic-ip-manager -n matrix-external | grep "IP 变化"
```

### 3. 健康检查
```yaml
# 健康检查配置
livenessProbe:
  exec:
    command:
    - /bin/sh
    - -c
    - "pgrep -f dynamic-ip-manager.sh"
  initialDelaySeconds: 30
  periodSeconds: 60

readinessProbe:
  exec:
    command:
    - /scripts/dynamic-ip-manager.sh
    - detect
    - --domain
    - "example.com"
  initialDelaySeconds: 10
  periodSeconds: 300  # 5分钟检查一次
  timeoutSeconds: 30
```

## 🔧 故障排除示例

### 1. 检测失败排查
```bash
# 步骤1: 检查网络连接
ping *******

# 步骤2: 检查DNS解析
nslookup ping0.cc
nslookup ipv4.icanhazip.com

# 步骤3: 手动测试服务
curl -v https://ping0.cc
curl -v https://ipv4.icanhazip.com

# 步骤4: 检查网络接口
ip addr show
ip route show

# 步骤5: 测试指定接口
curl --interface eth0 https://ping0.cc
```

### 2. 性能问题排查
```bash
# 检查系统负载
top
htop

# 检查网络延迟
ping -c 10 ping0.cc

# 检查DNS解析时间
time nslookup ping0.cc

# 调整超时设置
export NETWORK_TIMEOUT=20
./scripts/dynamic-ip-manager.sh detect --domain example.com
```

### 3. 配置问题排查
```bash
# 检查环境变量
env | grep -E "(DOMAIN|INTERFACE|TIMEOUT)"

# 检查脚本权限
ls -la scripts/dynamic-ip-manager.sh

# 检查依赖工具
which curl
which ip
which ping
```

## 📋 最佳实践

### 1. 生产环境建议
- 使用专用的网络接口进行IP检测
- 设置合理的检测间隔（建议10-30秒）
- 配置监控和告警
- 定期备份配置

### 2. 安全建议
- 使用HTTPS协议的IP检测服务
- 定期更新IP检测服务列表
- 限制脚本执行权限
- 监控异常的IP检测结果

### 3. 性能优化
- 根据网络环境调整超时设置
- 优先使用响应速度快的服务
- 避免过于频繁的检测
- 使用本地缓存减少重复检测

---

**文档版本**: 1.0  
**最后更新**: 2025-06-19  
**适用场景**: 所有网络环境  
**维护团队**: DevOps团队
