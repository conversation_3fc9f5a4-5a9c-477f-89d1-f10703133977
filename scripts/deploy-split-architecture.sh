#!/bin/bash

# Copyright 2025 New Vector Ltd
# SPDX-License-Identifier: AGPL-3.0-only

# 分离式部署架构快速部署脚本

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
分离式部署架构快速部署脚本

用法: $0 [选项] <部署类型> <域名>

部署类型:
  external    部署外部服务器（指路牌服务）
  internal    部署内部服务器（完整服务）
  both        同时部署两个服务器（用于测试）

选项:
  -h, --help              显示此帮助信息
  -n, --namespace NAME    指定 Kubernetes 命名空间 (默认: matrix)
  -r, --release NAME      指定 Helm release 名称
  --dry-run              只显示将要执行的命令，不实际执行
  --cloudflare-token TOKEN  Cloudflare API Token（内部服务器需要）
  --email EMAIL          Let's Encrypt 邮箱地址
  --internal-port PORT   内部服务器端口 (默认: 8443)

示例:
  # 部署外部服务器
  $0 external example.com --email <EMAIL>

  # 部署内部服务器
  $0 internal example.com --cloudflare-token your-token --email <EMAIL>

  # 同时部署（测试用）
  $0 both example.com --cloudflare-token your-token --email <EMAIL>

EOF
}

# 默认值
NAMESPACE="matrix"
RELEASE_NAME=""
DRY_RUN=false
CLOUDFLARE_TOKEN=""
EMAIL=""
INTERNAL_PORT="8443"
DEPLOYMENT_TYPE=""
DOMAIN=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -r|--release)
            RELEASE_NAME="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --cloudflare-token)
            CLOUDFLARE_TOKEN="$2"
            shift 2
            ;;
        --email)
            EMAIL="$2"
            shift 2
            ;;
        --internal-port)
            INTERNAL_PORT="$2"
            shift 2
            ;;
        external|internal|both)
            DEPLOYMENT_TYPE="$1"
            shift
            ;;
        *)
            if [[ -z "$DOMAIN" ]]; then
                DOMAIN="$1"
            else
                log_error "未知参数: $1"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# 验证必需参数
if [[ -z "$DEPLOYMENT_TYPE" ]]; then
    log_error "必须指定部署类型 (external/internal/both)"
    show_help
    exit 1
fi

if [[ -z "$DOMAIN" ]]; then
    log_error "必须指定域名"
    show_help
    exit 1
fi

if [[ -z "$EMAIL" ]]; then
    log_error "必须指定 Let's Encrypt 邮箱地址"
    show_help
    exit 1
fi

if [[ "$DEPLOYMENT_TYPE" == "internal" || "$DEPLOYMENT_TYPE" == "both" ]] && [[ -z "$CLOUDFLARE_TOKEN" ]]; then
    log_error "内部服务器部署需要 Cloudflare API Token"
    exit 1
fi

# 设置默认 release 名称
if [[ -z "$RELEASE_NAME" ]]; then
    case "$DEPLOYMENT_TYPE" in
        external)
            RELEASE_NAME="matrix-external"
            ;;
        internal)
            RELEASE_NAME="matrix-internal"
            ;;
        both)
            RELEASE_NAME="matrix"
            ;;
    esac
fi

# 执行命令函数
execute_command() {
    local cmd="$1"
    if [[ "$DRY_RUN" == "true" ]]; then
        echo "[DRY-RUN] $cmd"
    else
        log_info "执行: $cmd"
        eval "$cmd"
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装"
        exit 1
    fi
    
    if ! command -v helm &> /dev/null; then
        log_error "helm 未安装"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 创建命名空间
create_namespace() {
    log_info "创建命名空间: $NAMESPACE"
    execute_command "kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -"
}

# 创建 Cloudflare Secret
create_cloudflare_secret() {
    if [[ -n "$CLOUDFLARE_TOKEN" ]]; then
        log_info "创建 Cloudflare API Token Secret"
        execute_command "kubectl create secret generic cloudflare-api-token --from-literal=api-token='$CLOUDFLARE_TOKEN' -n $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -"
    fi
}

# 创建 ClusterIssuer
create_cluster_issuers() {
    log_info "创建 cert-manager ClusterIssuer"

    # 外部服务器 ClusterIssuer
    if [[ "$DEPLOYMENT_TYPE" == "external" || "$DEPLOYMENT_TYPE" == "both" ]]; then
        local external_issuer_yaml="apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: $EMAIL
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx"

        if [[ "$DRY_RUN" == "true" ]]; then
            echo "[DRY-RUN] kubectl apply -f - <<< '$external_issuer_yaml'"
        else
            echo "$external_issuer_yaml" | kubectl apply -f -
        fi
    fi
    
    # 内部服务器 ClusterIssuer
    if [[ "$DEPLOYMENT_TYPE" == "internal" || "$DEPLOYMENT_TYPE" == "both" ]]; then
        local internal_issuer_yaml="apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: cloudflare-letsencrypt
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: $EMAIL
    privateKeySecretRef:
      name: cloudflare-letsencrypt
    solvers:
    - dns01:
        cloudflare:
          apiTokenSecretRef:
            name: cloudflare-api-token
            key: api-token"

        if [[ "$DRY_RUN" == "true" ]]; then
            echo "[DRY-RUN] kubectl apply -f - <<< '$internal_issuer_yaml'"
        else
            echo "$internal_issuer_yaml" | kubectl apply -f -
        fi
    fi
}

# 部署外部服务器
deploy_external_server() {
    log_info "部署外部服务器..."
    
    local values_file="charts/matrix-stack/user_values/external-server-example.yaml"
    local release_name="${RELEASE_NAME}-external"
    
    if [[ "$DEPLOYMENT_TYPE" == "external" ]]; then
        release_name="$RELEASE_NAME"
    fi
    
    execute_command "helm upgrade --install $release_name ./charts/matrix-stack \\
        -f $values_file \\
        --set serverName=$DOMAIN \\
        --set wellKnownDelegation.ingress.host=$DOMAIN \\
        --set dynamicIpUpdater.internalServer.baseHost=$DOMAIN \\
        --set dynamicIpUpdater.internalServer.port=$INTERNAL_PORT \\
        -n $NAMESPACE"
    
    log_success "外部服务器部署完成"
}

# 部署内部服务器
deploy_internal_server() {
    log_info "部署内部服务器..."
    
    local values_file="charts/matrix-stack/user_values/internal-server-example.yaml"
    local release_name="${RELEASE_NAME}-internal"
    
    if [[ "$DEPLOYMENT_TYPE" == "internal" ]]; then
        release_name="$RELEASE_NAME"
    fi
    
    execute_command "helm upgrade --install $release_name ./charts/matrix-stack \\
        -f $values_file \\
        --set serverName=$DOMAIN \\
        --set synapse.ingress.host=matrix.$DOMAIN \\
        --set matrixAuthenticationService.ingress.host=mas.$DOMAIN \\
        --set elementWeb.ingress.host=element.$DOMAIN \\
        --set matrixRTC.ingress.host=rtc.$DOMAIN \\
        -n $NAMESPACE"
    
    log_success "内部服务器部署完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署状态..."
    
    execute_command "kubectl get pods -n $NAMESPACE"
    execute_command "kubectl get ingress -n $NAMESPACE"
    execute_command "kubectl get certificates -n $NAMESPACE"
    
    if [[ "$DEPLOYMENT_TYPE" == "external" || "$DEPLOYMENT_TYPE" == "both" ]]; then
        log_info "验证 well-known 委托..."
        if [[ "$DRY_RUN" == "false" ]]; then
            sleep 30  # 等待服务启动
            curl -f "https://$DOMAIN/.well-known/matrix/server" || log_warning "well-known/server 验证失败"
            curl -f "https://$DOMAIN/.well-known/matrix/client" || log_warning "well-known/client 验证失败"
        fi
    fi
    
    log_success "部署验证完成"
}

# 主函数
main() {
    log_info "开始分离式部署架构部署"
    log_info "部署类型: $DEPLOYMENT_TYPE"
    log_info "域名: $DOMAIN"
    log_info "命名空间: $NAMESPACE"
    log_info "Release 名称: $RELEASE_NAME"
    
    check_dependencies
    create_namespace
    create_cloudflare_secret
    create_cluster_issuers
    
    case "$DEPLOYMENT_TYPE" in
        external)
            deploy_external_server
            ;;
        internal)
            deploy_internal_server
            ;;
        both)
            deploy_external_server
            deploy_internal_server
            ;;
    esac
    
    verify_deployment
    
    log_success "分离式部署架构部署完成！"
    
    # 显示后续步骤
    cat << EOF

后续步骤:
1. 检查所有 Pod 是否正常运行: kubectl get pods -n $NAMESPACE
2. 检查证书是否成功申请: kubectl get certificates -n $NAMESPACE
3. 测试 well-known 委托: curl https://$DOMAIN/.well-known/matrix/server
4. 配置 DNS 记录指向正确的服务器
5. 测试 Matrix 客户端连接

EOF
}

# 运行主函数
main
