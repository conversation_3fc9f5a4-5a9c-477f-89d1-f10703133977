#!/bin/bash

# RouterOS API 快速连接测试脚本
# Copyright 2025 New Vector Ltd
# SPDX-License-Identifier: AGPL-3.0-only

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示帮助信息
show_help() {
    cat << EOF
RouterOS API 快速连接测试

用法: $0 RouterOS_IP [用户名] [密码]

参数:
  RouterOS_IP    RouterOS设备的IP地址
  用户名         RouterOS用户名 (默认: admin)
  密码           RouterOS密码 (默认: 空密码)

示例:
  $0 ***********
  $0 *********** admin
  $0 *********** admin mypassword
  $0 *********** api-user securepass

EOF
}

# 参数检查
if [[ $# -eq 0 ]] || [[ "$1" == "-h" ]] || [[ "$1" == "--help" ]]; then
    show_help
    exit 0
fi

ROUTEROS_HOST="$1"
ROUTEROS_USERNAME="${2:-admin}"
ROUTEROS_PASSWORD="${3:-}"

echo "=========================================="
echo "RouterOS API 快速连接测试"
echo "=========================================="
echo "目标设备: $ROUTEROS_HOST"
echo "用户名: $ROUTEROS_USERNAME"
echo "密码: $(if [[ -n "$ROUTEROS_PASSWORD" ]]; then echo "已设置"; else echo "空密码"; fi)"
echo "=========================================="
echo

# 1. 基本网络测试
log_info "1. 测试网络连通性..."
if ping -c 2 -W 3 "$ROUTEROS_HOST" >/dev/null 2>&1; then
    log_success "Ping测试成功"
else
    log_warning "Ping测试失败（设备可能禁用了ICMP响应）"
fi

# 2. 端口连通性测试
log_info "2. 测试常见API端口..."
declare -A ports=(
    ["443"]="HTTPS API"
    ["80"]="HTTP API"
    ["8728"]="API Binary"
    ["8729"]="API-SSL Binary"
)

available_ports=()
for port in "${!ports[@]}"; do
    if timeout 3 bash -c "</dev/tcp/$ROUTEROS_HOST/$port" 2>/dev/null; then
        log_success "端口 $port 可达 (${ports[$port]})"
        available_ports+=("$port")
    else
        log_info "端口 $port 不可达"
    fi
done

if [[ ${#available_ports[@]} -eq 0 ]]; then
    log_error "没有发现可用的API端口"
    log_error "请检查："
    log_error "  1. RouterOS设备是否启用了API服务"
    log_error "  2. 防火墙是否阻止了连接"
    log_error "  3. IP地址是否正确"
    exit 1
fi

echo

# 3. API连接测试
log_info "3. 测试API连接..."

# 测试HTTPS API
if [[ " ${available_ports[*]} " =~ " 443 " ]]; then
    log_info "测试HTTPS API (端口443)..."
    
    # 构建认证字符串
    if [[ -n "$ROUTEROS_PASSWORD" ]]; then
        auth="$ROUTEROS_USERNAME:$ROUTEROS_PASSWORD"
    else
        auth="$ROUTEROS_USERNAME:"
    fi
    
    # 测试API调用
    response=$(timeout 10 curl -s -k -w "HTTPCODE:%{http_code}" -u "$auth" \
        "https://$ROUTEROS_HOST:443/rest/system/identity" 2>/dev/null)
    
    if [[ $? -eq 0 ]]; then
        http_code=$(echo "$response" | grep -o "HTTPCODE:[0-9]*" | cut -d: -f2)
        api_response=$(echo "$response" | sed 's/HTTPCODE:[0-9]*$//')
        
        case $http_code in
            200)
                log_success "HTTPS API连接成功！"
                log_info "系统信息:"
                if command -v jq >/dev/null 2>&1; then
                    echo "$api_response" | jq . 2>/dev/null || echo "$api_response"
                else
                    echo "$api_response"
                fi
                echo
                log_success "✅ RouterOS API连接测试通过"
                log_info "您可以使用以下配置："
                echo "export ROUTEROS_HOST=\"$ROUTEROS_HOST\""
                echo "export ROUTEROS_USERNAME=\"$ROUTEROS_USERNAME\""
                echo "export ROUTEROS_PASSWORD=\"$ROUTEROS_PASSWORD\""
                echo "export ROUTEROS_PORT=\"443\""
                echo "export ROUTEROS_USE_HTTPS=\"true\""
                exit 0
                ;;
            401)
                log_error "认证失败 - 用户名或密码错误"
                log_info "请检查："
                log_info "  1. 用户名是否正确"
                log_info "  2. 密码是否正确"
                log_info "  3. 用户是否存在"
                ;;
            403)
                log_error "权限不足 - 用户没有API访问权限"
                log_info "请在RouterOS中执行："
                log_info "  /user group print"
                log_info "  /user print"
                ;;
            404)
                log_error "API服务未启用或路径错误"
                log_info "请在RouterOS中执行："
                log_info "  /ip service enable api-ssl"
                log_info "  /ip service set api-ssl port=443"
                ;;
            *)
                log_error "API请求失败，HTTP状态码: $http_code"
                ;;
        esac
    else
        log_error "HTTPS API连接失败"
    fi
fi

# 测试HTTP API
if [[ " ${available_ports[*]} " =~ " 80 " ]]; then
    log_info "测试HTTP API (端口80)..."
    
    # 构建认证字符串
    if [[ -n "$ROUTEROS_PASSWORD" ]]; then
        auth="$ROUTEROS_USERNAME:$ROUTEROS_PASSWORD"
    else
        auth="$ROUTEROS_USERNAME:"
    fi
    
    # 测试API调用
    response=$(timeout 10 curl -s -w "HTTPCODE:%{http_code}" -u "$auth" \
        "http://$ROUTEROS_HOST:80/rest/system/identity" 2>/dev/null)
    
    if [[ $? -eq 0 ]]; then
        http_code=$(echo "$response" | grep -o "HTTPCODE:[0-9]*" | cut -d: -f2)
        api_response=$(echo "$response" | sed 's/HTTPCODE:[0-9]*$//')
        
        if [[ "$http_code" == "200" ]]; then
            log_success "HTTP API连接成功！"
            log_info "系统信息:"
            if command -v jq >/dev/null 2>&1; then
                echo "$api_response" | jq . 2>/dev/null || echo "$api_response"
            else
                echo "$api_response"
            fi
            echo
            log_success "✅ RouterOS API连接测试通过"
            log_info "您可以使用以下配置："
            echo "export ROUTEROS_HOST=\"$ROUTEROS_HOST\""
            echo "export ROUTEROS_USERNAME=\"$ROUTEROS_USERNAME\""
            echo "export ROUTEROS_PASSWORD=\"$ROUTEROS_PASSWORD\""
            echo "export ROUTEROS_PORT=\"80\""
            echo "export ROUTEROS_USE_HTTPS=\"false\""
            exit 0
        fi
    fi
fi

echo
log_error "❌ RouterOS API连接测试失败"
log_info "建议的解决步骤："
log_info "1. 检查RouterOS API服务配置："
log_info "   /ip service print"
log_info "   /ip service enable api-ssl"
log_info "   /ip service set api-ssl port=443"
log_info ""
log_info "2. 检查用户配置："
log_info "   /user print"
log_info "   /user set admin password=your-password"
log_info ""
log_info "3. 运行详细诊断："
log_info "   ./scripts/routeros-troubleshoot.sh --host $ROUTEROS_HOST --username $ROUTEROS_USERNAME --password '$ROUTEROS_PASSWORD'"
log_info ""
log_info "4. 查看故障排除指南："
log_info "   cat RouterOS-API-故障排除指南.md"

exit 1
