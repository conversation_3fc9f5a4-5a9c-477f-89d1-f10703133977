# RouterOS API 动态IP检测配置示例
# Copyright 2025 New Vector Ltd
# SPDX-License-Identifier: AGPL-3.0-only

apiVersion: v1
kind: ConfigMap
metadata:
  name: routeros-dynamic-ip-config
  namespace: matrix-external
  labels:
    app.kubernetes.io/name: dynamic-ip-manager
    app.kubernetes.io/component: config
    app.kubernetes.io/version: routeros-api
data:
  # 基础配置
  domain: "example.com"
  internal-port: "8443"
  check-interval: "5"  # RouterOS API检测间隔（秒）
  
  # RouterOS API 配置
  routeros-host: "***********"          # RouterOS设备IP地址
  routeros-username: "admin"             # RouterOS用户名
  routeros-port: "443"                   # RouterOS REST API端口
  routeros-use-https: "true"             # 是否使用HTTPS
  routeros-timeout: "10"                 # API请求超时时间（秒）
  routeros-retry-count: "3"              # 连接失败重试次数
  routeros-retry-delay: "2"              # 重试间隔（秒）
  
  # WAN接口配置（可选）
  # routeros-wan-interface: "ether1"     # 指定WAN接口名称，留空则自动检测
  
  # 日志配置
  log-level: "info"
  enable-detailed-logging: "true"

---
apiVersion: v1
kind: Secret
metadata:
  name: routeros-api-credentials
  namespace: matrix-external
  labels:
    app.kubernetes.io/name: dynamic-ip-manager
    app.kubernetes.io/component: credentials
type: Opaque
stringData:
  # RouterOS API 认证信息
  routeros-password: "your-routeros-password"  # 请替换为实际密码
  
  # Cloudflare API 认证信息（用于DNS更新）
  cloudflare-api-token: "your-cloudflare-api-token"
  cloudflare-zone-id: "your-cloudflare-zone-id"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: routeros-dynamic-ip-manager
  namespace: matrix-external
  labels:
    app.kubernetes.io/name: dynamic-ip-manager
    app.kubernetes.io/component: manager
    app.kubernetes.io/version: routeros-api
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: dynamic-ip-manager
  template:
    metadata:
      labels:
        app.kubernetes.io/name: dynamic-ip-manager
    spec:
      serviceAccountName: dynamic-ip-manager
      containers:
      - name: dynamic-ip-manager
        image: alpine:latest
        command:
        - /bin/sh
        - -c
        - |
          # 安装依赖
          apk add --no-cache curl bash jq
          
          # 启动监控
          exec /usr/local/bin/dynamic-ip-manager.sh monitor \
            --domain "${DOMAIN}" \
            --internal-port "${INTERNAL_PORT}" \
            --check-interval "${CHECK_INTERVAL}" \
            --routeros-host "${ROUTEROS_HOST}" \
            --routeros-username "${ROUTEROS_USERNAME}" \
            --routeros-port "${ROUTEROS_PORT}" \
            --routeros-timeout "${ROUTEROS_TIMEOUT}"
        env:
        # 基础配置
        - name: DOMAIN
          valueFrom:
            configMapKeyRef:
              name: routeros-dynamic-ip-config
              key: domain
        - name: INTERNAL_PORT
          valueFrom:
            configMapKeyRef:
              name: routeros-dynamic-ip-config
              key: internal-port
        - name: CHECK_INTERVAL
          valueFrom:
            configMapKeyRef:
              name: routeros-dynamic-ip-config
              key: check-interval
        
        # RouterOS API 配置
        - name: ROUTEROS_HOST
          valueFrom:
            configMapKeyRef:
              name: routeros-dynamic-ip-config
              key: routeros-host
        - name: ROUTEROS_USERNAME
          valueFrom:
            configMapKeyRef:
              name: routeros-dynamic-ip-config
              key: routeros-username
        - name: ROUTEROS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: routeros-api-credentials
              key: routeros-password
        - name: ROUTEROS_PORT
          valueFrom:
            configMapKeyRef:
              name: routeros-dynamic-ip-config
              key: routeros-port
        - name: ROUTEROS_USE_HTTPS
          valueFrom:
            configMapKeyRef:
              name: routeros-dynamic-ip-config
              key: routeros-use-https
        - name: ROUTEROS_TIMEOUT
          valueFrom:
            configMapKeyRef:
              name: routeros-dynamic-ip-config
              key: routeros-timeout
        - name: ROUTEROS_RETRY_COUNT
          valueFrom:
            configMapKeyRef:
              name: routeros-dynamic-ip-config
              key: routeros-retry-count
        - name: ROUTEROS_RETRY_DELAY
          valueFrom:
            configMapKeyRef:
              name: routeros-dynamic-ip-config
              key: routeros-retry-delay
        
        # Cloudflare API 配置
        - name: CLOUDFLARE_API_TOKEN
          valueFrom:
            secretKeyRef:
              name: routeros-api-credentials
              key: cloudflare-api-token
        - name: CLOUDFLARE_ZONE_ID
          valueFrom:
            secretKeyRef:
              name: routeros-api-credentials
              key: cloudflare-zone-id
              optional: true
        
        volumeMounts:
        - name: scripts
          mountPath: /usr/local/bin/dynamic-ip-manager.sh
          subPath: dynamic-ip-manager.sh
        - name: config
          mountPath: /config
        
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - "pgrep -f dynamic-ip-manager.sh"
          initialDelaySeconds: 30
          periodSeconds: 60
        
        readinessProbe:
          exec:
            command:
            - /usr/local/bin/dynamic-ip-manager.sh
            - detect
            - --domain
            - "${DOMAIN}"
            - --routeros-host
            - "${ROUTEROS_HOST}"
          initialDelaySeconds: 10
          periodSeconds: 30
          timeoutSeconds: 30
      
      volumes:
      - name: scripts
        configMap:
          name: dynamic-ip-scripts
          defaultMode: 0755
      - name: config
        configMap:
          name: routeros-dynamic-ip-config
      
      restartPolicy: Always

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: dynamic-ip-manager
  namespace: matrix-external
  labels:
    app.kubernetes.io/name: dynamic-ip-manager

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: matrix-external
  name: dynamic-ip-manager
rules:
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "create", "update", "patch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: dynamic-ip-manager
  namespace: matrix-external
subjects:
- kind: ServiceAccount
  name: dynamic-ip-manager
  namespace: matrix-external
roleRef:
  kind: Role
  name: dynamic-ip-manager
  apiGroup: rbac.authorization.k8s.io
