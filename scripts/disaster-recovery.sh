#!/bin/bash

# Copyright 2025 New Vector Ltd
# SPDX-License-Identifier: AGPL-3.0-only

# 内部服务器故障恢复脚本

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
NAMESPACE="${NAMESPACE:-matrix-internal}"
RELEASE_NAME="${RELEASE_NAME:-matrix-internal}"
BACKUP_DIR="${BACKUP_DIR:-./backups}"
ALERT_WEBHOOK="${ALERT_WEBHOOK:-}"

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 发送告警通知
send_alert() {
    local message="$1"
    local severity="${2:-warning}"
    
    log_info "发送告警通知: $message"
    
    if [[ -n "$ALERT_WEBHOOK" ]]; then
        curl -X POST "$ALERT_WEBHOOK" \
            -H "Content-Type: application/json" \
            -d "{
                \"text\": \"🚨 Matrix 内部服务器告警\",
                \"attachments\": [{
                    \"color\": \"$([ "$severity" = "error" ] && echo "danger" || echo "warning")\",
                    \"fields\": [{
                        \"title\": \"消息\",
                        \"value\": \"$message\",
                        \"short\": false
                    }, {
                        \"title\": \"命名空间\",
                        \"value\": \"$NAMESPACE\",
                        \"short\": true
                    }, {
                        \"title\": \"时间\",
                        \"value\": \"$(date)\",
                        \"short\": true
                    }]
                }]
            }" || log_warning "告警通知发送失败"
    fi
}

# 检查服务健康状态
check_service_health() {
    local service_name="$1"
    local namespace="$2"
    
    # 检查 Pod 状态
    local pod_status
    pod_status=$(kubectl get pods -n "$namespace" -l "app.kubernetes.io/name=$service_name" -o jsonpath='{.items[*].status.phase}' 2>/dev/null || echo "")
    
    if [[ "$pod_status" == *"Running"* ]]; then
        return 0
    else
        return 1
    fi
}

# 重启服务
restart_service() {
    local service_name="$1"
    local namespace="$2"
    
    log_info "重启服务: $service_name"
    
    # 获取 Deployment 名称
    local deployment
    deployment=$(kubectl get deployments -n "$namespace" -l "app.kubernetes.io/name=$service_name" -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [[ -n "$deployment" ]]; then
        kubectl rollout restart deployment "$deployment" -n "$namespace"
        kubectl rollout status deployment "$deployment" -n "$namespace" --timeout=300s
        log_success "服务 $service_name 重启完成"
    else
        log_error "未找到服务 $service_name 的 Deployment"
        return 1
    fi
}

# 检查并修复证书问题
fix_certificate_issues() {
    log_info "检查证书状态..."
    
    local failed_certs=()
    
    # 获取所有证书
    while IFS= read -r line; do
        local cert_name status
        cert_name=$(echo "$line" | awk '{print $1}')
        status=$(echo "$line" | awk '{print $2}')
        
        if [[ "$status" != "True" ]]; then
            failed_certs+=("$cert_name")
        fi
    done < <(kubectl get certificates -n "$NAMESPACE" --no-headers 2>/dev/null || true)
    
    # 修复失败的证书
    for cert in "${failed_certs[@]}"; do
        log_warning "证书 $cert 状态异常，尝试修复..."
        
        # 删除证书请求，让 cert-manager 重新创建
        kubectl delete certificaterequest -n "$NAMESPACE" -l "cert-manager.io/certificate-name=$cert" --ignore-not-found=true
        
        # 重新触发证书申请
        kubectl annotate certificate "$cert" -n "$NAMESPACE" cert-manager.io/issue-temporary-certificate- --overwrite
        
        log_info "已触发证书 $cert 的重新申请"
    done
    
    if [[ ${#failed_certs[@]} -gt 0 ]]; then
        send_alert "发现 ${#failed_certs[@]} 个证书问题，已尝试修复: ${failed_certs[*]}" "warning"
    fi
}

# 检查存储空间
check_storage_space() {
    log_info "检查存储空间..."
    
    # 检查 PVC 使用情况
    while IFS= read -r line; do
        local pvc_name capacity used_percent
        pvc_name=$(echo "$line" | awk '{print $1}')
        capacity=$(echo "$line" | awk '{print $4}')
        
        # 这里需要实际的存储使用率检查逻辑
        # 简化示例，实际需要根据存储类型实现
        log_info "PVC: $pvc_name, 容量: $capacity"
        
    done < <(kubectl get pvc -n "$NAMESPACE" --no-headers 2>/dev/null || true)
}

# 清理临时文件和日志
cleanup_temporary_files() {
    log_info "清理临时文件..."
    
    # 清理 Synapse 临时文件（如果可以访问）
    local synapse_pods
    synapse_pods=$(kubectl get pods -n "$NAMESPACE" -l "app.kubernetes.io/name=synapse" -o jsonpath='{.items[*].metadata.name}' 2>/dev/null || echo "")
    
    for pod in $synapse_pods; do
        log_info "清理 Pod $pod 的临时文件..."
        kubectl exec "$pod" -n "$NAMESPACE" -- find /tmp -type f -mtime +7 -delete 2>/dev/null || true
    done
}

# 数据库健康检查
check_database_health() {
    log_info "检查数据库健康状态..."
    
    # 检查 PostgreSQL Pod
    if check_service_health "postgres" "$NAMESPACE"; then
        log_success "PostgreSQL 服务正常"
    else
        log_error "PostgreSQL 服务异常"
        send_alert "PostgreSQL 服务异常，需要人工介入" "error"
        return 1
    fi
    
    # 检查数据库连接（通过 Synapse）
    local synapse_pod
    synapse_pod=$(kubectl get pods -n "$NAMESPACE" -l "app.kubernetes.io/name=synapse" -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [[ -n "$synapse_pod" ]]; then
        if kubectl exec "$synapse_pod" -n "$NAMESPACE" -- python -c "
import psycopg2
import os
try:
    conn = psycopg2.connect(
        host=os.environ.get('SYNAPSE_POSTGRES_HOST', 'localhost'),
        port=os.environ.get('SYNAPSE_POSTGRES_PORT', '5432'),
        user=os.environ.get('SYNAPSE_POSTGRES_USER', 'synapse'),
        password=os.environ.get('SYNAPSE_POSTGRES_PASSWORD', ''),
        database=os.environ.get('SYNAPSE_POSTGRES_DATABASE', 'synapse')
    )
    conn.close()
    print('Database connection successful')
except Exception as e:
    print(f'Database connection failed: {e}')
    exit(1)
" 2>/dev/null; then
            log_success "数据库连接正常"
        else
            log_error "数据库连接失败"
            send_alert "数据库连接失败，需要检查连接配置" "error"
            return 1
        fi
    fi
}

# 自动修复常见问题
auto_fix_common_issues() {
    log_info "开始自动修复常见问题..."
    
    local services=("synapse" "matrix-authentication-service" "element-web" "matrix-rtc" "haproxy")
    local restart_needed=()
    
    # 检查各个服务状态
    for service in "${services[@]}"; do
        if ! check_service_health "$service" "$NAMESPACE"; then
            log_warning "服务 $service 状态异常"
            restart_needed+=("$service")
        fi
    done
    
    # 重启异常服务
    for service in "${restart_needed[@]}"; do
        if restart_service "$service" "$NAMESPACE"; then
            log_success "服务 $service 重启成功"
        else
            log_error "服务 $service 重启失败"
            send_alert "服务 $service 重启失败，需要人工介入" "error"
        fi
    done
    
    # 修复证书问题
    fix_certificate_issues
    
    # 检查存储空间
    check_storage_space
    
    # 清理临时文件
    cleanup_temporary_files
    
    # 检查数据库
    check_database_health
    
    if [[ ${#restart_needed[@]} -gt 0 ]]; then
        send_alert "自动修复完成，重启了 ${#restart_needed[@]} 个服务: ${restart_needed[*]}" "warning"
    else
        log_success "所有服务状态正常，无需修复"
    fi
}

# 创建备份
create_backup() {
    log_info "创建配置备份..."
    
    mkdir -p "$BACKUP_DIR"
    local backup_file="${BACKUP_DIR}/backup-$(date +%Y%m%d-%H%M%S).tar.gz"
    
    # 备份 Helm values
    helm get values "$RELEASE_NAME" -n "$NAMESPACE" > "${BACKUP_DIR}/values-$(date +%Y%m%d-%H%M%S).yaml" 2>/dev/null || true
    
    # 备份 Secrets
    kubectl get secrets -n "$NAMESPACE" -o yaml > "${BACKUP_DIR}/secrets-$(date +%Y%m%d-%H%M%S).yaml" 2>/dev/null || true
    
    # 备份 ConfigMaps
    kubectl get configmaps -n "$NAMESPACE" -o yaml > "${BACKUP_DIR}/configmaps-$(date +%Y%m%d-%H%M%S).yaml" 2>/dev/null || true
    
    # 创建压缩包
    tar -czf "$backup_file" -C "$BACKUP_DIR" . 2>/dev/null || true
    
    log_success "备份创建完成: $backup_file"
}

# 显示帮助信息
show_help() {
    cat << EOF
内部服务器故障恢复脚本

用法: $0 [选项] <操作>

操作:
  health-check    执行健康检查
  auto-fix        自动修复常见问题
  restart-all     重启所有服务
  backup          创建配置备份
  fix-certs       修复证书问题

选项:
  -h, --help                  显示此帮助信息
  -n, --namespace NAME        Kubernetes 命名空间
  -r, --release NAME          Helm release 名称
  --alert-webhook URL         告警通知 Webhook URL

示例:
  $0 health-check
  $0 auto-fix --namespace matrix-internal
  $0 restart-all --alert-webhook https://hooks.slack.com/...

EOF
}

# 主函数
main() {
    local action="${1:-health-check}"
    
    case "$action" in
        health-check)
            log_info "执行健康检查..."
            auto_fix_common_issues
            ;;
        auto-fix)
            log_info "执行自动修复..."
            auto_fix_common_issues
            ;;
        restart-all)
            log_info "重启所有服务..."
            local services=("synapse" "matrix-authentication-service" "element-web" "matrix-rtc" "haproxy")
            for service in "${services[@]}"; do
                restart_service "$service" "$NAMESPACE"
            done
            ;;
        backup)
            create_backup
            ;;
        fix-certs)
            fix_certificate_issues
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -r|--release)
            RELEASE_NAME="$2"
            shift 2
            ;;
        --alert-webhook)
            ALERT_WEBHOOK="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            main "$1"
            exit 0
            ;;
    esac
done

# 如果没有指定操作，执行默认的健康检查
main "health-check"
