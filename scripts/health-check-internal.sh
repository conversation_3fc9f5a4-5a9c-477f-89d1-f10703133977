#!/bin/bash

# Copyright 2025 New Vector Ltd
# SPDX-License-Identifier: AGPL-3.0-only

# 内部服务器健康检查脚本

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
NAMESPACE="${NAMESPACE:-matrix-internal}"
RELEASE_NAME="${RELEASE_NAME:-matrix-internal}"
DOMAIN=""
TIMEOUT="${TIMEOUT:-300}"

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示帮助信息
show_help() {
    cat << EOF
内部服务器健康检查脚本

用法: $0 [选项]

必需参数:
  --domain DOMAIN              主域名 (如: example.com)

可选参数:
  -h, --help                  显示此帮助信息
  -n, --namespace NAME        Kubernetes 命名空间 (默认: matrix-internal)
  -r, --release NAME          Helm release 名称 (默认: matrix-internal)
  --timeout SECONDS           检查超时时间 (默认: 300秒)
  --detailed                  显示详细检查信息

示例:
  $0 --domain example.com
  $0 --domain example.com --namespace matrix --detailed

EOF
}

# 参数解析
DETAILED=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --domain)
            DOMAIN="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -r|--release)
            RELEASE_NAME="$2"
            shift 2
            ;;
        --timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        --detailed)
            DETAILED=true
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证必需参数
if [[ -z "$DOMAIN" ]]; then
    log_error "缺少必需参数: --domain"
    show_help
    exit 1
fi

# 检查 Kubernetes 连接
check_kubernetes_connection() {
    log_info "检查 Kubernetes 连接..."
    
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到 Kubernetes 集群"
        exit 1
    fi
    
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_error "命名空间 $NAMESPACE 不存在"
        exit 1
    fi
    
    log_success "Kubernetes 连接正常"
}

# 检查 Helm Release
check_helm_release() {
    log_info "检查 Helm Release..."
    
    if ! helm list -n "$NAMESPACE" | grep -q "$RELEASE_NAME"; then
        log_error "Helm release $RELEASE_NAME 不存在"
        exit 1
    fi
    
    local status
    status=$(helm status "$RELEASE_NAME" -n "$NAMESPACE" -o json | jq -r '.info.status')
    
    if [[ "$status" != "deployed" ]]; then
        log_error "Helm release 状态异常: $status"
        exit 1
    fi
    
    log_success "Helm Release 状态正常"
}

# 检查 Pod 状态
check_pods_status() {
    log_info "检查 Pod 状态..."
    
    local failed_pods=()
    local pending_pods=()
    
    # 获取所有 Pod
    while IFS= read -r line; do
        local pod_name status ready
        pod_name=$(echo "$line" | awk '{print $1}')
        status=$(echo "$line" | awk '{print $3}')
        ready=$(echo "$line" | awk '{print $2}')
        
        if [[ "$status" == "Running" ]]; then
            if [[ "$ready" == *"/"* ]]; then
                local ready_count total_count
                ready_count=$(echo "$ready" | cut -d'/' -f1)
                total_count=$(echo "$ready" | cut -d'/' -f2)
                
                if [[ "$ready_count" != "$total_count" ]]; then
                    failed_pods+=("$pod_name (Not Ready: $ready)")
                fi
            fi
        elif [[ "$status" == "Pending" ]]; then
            pending_pods+=("$pod_name")
        elif [[ "$status" != "Completed" ]]; then
            failed_pods+=("$pod_name ($status)")
        fi
        
        if [[ "$DETAILED" == "true" ]]; then
            log_info "Pod: $pod_name, Status: $status, Ready: $ready"
        fi
    done < <(kubectl get pods -n "$NAMESPACE" --no-headers)
    
    if [[ ${#failed_pods[@]} -gt 0 ]]; then
        log_error "以下 Pod 状态异常:"
        printf '%s\n' "${failed_pods[@]}"
        return 1
    fi
    
    if [[ ${#pending_pods[@]} -gt 0 ]]; then
        log_warning "以下 Pod 仍在等待中:"
        printf '%s\n' "${pending_pods[@]}"
    fi
    
    log_success "所有 Pod 状态正常"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    local services=("synapse" "matrix-authentication-service" "element-web" "matrix-rtc" "haproxy")
    
    for service in "${services[@]}"; do
        local service_name="${RELEASE_NAME}-${service}"
        
        if kubectl get service "$service_name" -n "$NAMESPACE" &> /dev/null; then
            log_success "服务 $service_name 存在"
        else
            log_warning "服务 $service_name 不存在"
        fi
    done
}

# 检查 Ingress 状态
check_ingress() {
    log_info "检查 Ingress 状态..."
    
    local ingresses=("synapse" "matrix-authentication-service" "element-web" "matrix-rtc")
    
    for ingress in "${ingresses[@]}"; do
        local ingress_name="${RELEASE_NAME}-${ingress}"
        
        if kubectl get ingress "$ingress_name" -n "$NAMESPACE" &> /dev/null; then
            local hosts
            hosts=$(kubectl get ingress "$ingress_name" -n "$NAMESPACE" -o jsonpath='{.spec.rules[*].host}')
            log_success "Ingress $ingress_name 存在，主机: $hosts"
        else
            log_warning "Ingress $ingress_name 不存在"
        fi
    done
}

# 检查证书状态
check_certificates() {
    log_info "检查证书状态..."
    
    local certificates
    certificates=$(kubectl get certificates -n "$NAMESPACE" --no-headers 2>/dev/null || true)
    
    if [[ -z "$certificates" ]]; then
        log_warning "未找到证书资源"
        return
    fi
    
    while IFS= read -r line; do
        local cert_name status
        cert_name=$(echo "$line" | awk '{print $1}')
        status=$(echo "$line" | awk '{print $2}')
        
        if [[ "$status" == "True" ]]; then
            log_success "证书 $cert_name 状态正常"
        else
            log_error "证书 $cert_name 状态异常: $status"
        fi
        
        if [[ "$DETAILED" == "true" ]]; then
            kubectl describe certificate "$cert_name" -n "$NAMESPACE"
        fi
    done <<< "$certificates"
}

# 检查 HTTPS 访问
check_https_access() {
    log_info "检查 HTTPS 访问..."
    
    local endpoints=(
        "https://matrix.$DOMAIN/_matrix/client/versions"
        "https://mas.$DOMAIN/health"
        "https://element.$DOMAIN/health"
        "https://rtc.$DOMAIN/health"
    )
    
    for endpoint in "${endpoints[@]}"; do
        if curl -s -f --max-time 10 "$endpoint" > /dev/null 2>&1; then
            log_success "HTTPS 访问正常: $endpoint"
        else
            log_error "HTTPS 访问失败: $endpoint"
        fi
    done
}

# 检查 Matrix 功能
check_matrix_functionality() {
    log_info "检查 Matrix 功能..."
    
    # 检查 Synapse 版本信息
    local synapse_version
    if synapse_version=$(curl -s --max-time 10 "https://matrix.$DOMAIN/_matrix/client/versions" | jq -r '.versions[0]' 2>/dev/null); then
        log_success "Synapse 版本检查通过: $synapse_version"
    else
        log_error "Synapse 版本检查失败"
    fi
    
    # 检查 MAS 健康状态
    if curl -s -f --max-time 10 "https://mas.$DOMAIN/health" > /dev/null 2>&1; then
        log_success "MAS 健康检查通过"
    else
        log_error "MAS 健康检查失败"
    fi
}

# 生成健康检查报告
generate_health_report() {
    log_info "生成健康检查报告..."
    
    local report_file="health-report-$(date +%Y%m%d-%H%M%S).txt"
    
    {
        echo "内部服务器健康检查报告"
        echo "========================"
        echo "检查时间: $(date)"
        echo "域名: $DOMAIN"
        echo "命名空间: $NAMESPACE"
        echo "Release: $RELEASE_NAME"
        echo ""
        
        echo "Pod 状态:"
        kubectl get pods -n "$NAMESPACE"
        echo ""
        
        echo "服务状态:"
        kubectl get services -n "$NAMESPACE"
        echo ""
        
        echo "Ingress 状态:"
        kubectl get ingress -n "$NAMESPACE"
        echo ""
        
        echo "证书状态:"
        kubectl get certificates -n "$NAMESPACE" 2>/dev/null || echo "无证书资源"
        echo ""
        
    } > "$report_file"
    
    log_success "健康检查报告已生成: $report_file"
}

# 主函数
main() {
    log_info "开始内部服务器健康检查"
    log_info "域名: $DOMAIN"
    log_info "命名空间: $NAMESPACE"
    log_info "Release: $RELEASE_NAME"
    
    local start_time
    start_time=$(date +%s)
    
    check_kubernetes_connection
    check_helm_release
    check_pods_status
    check_services
    check_ingress
    check_certificates
    check_https_access
    check_matrix_functionality
    
    if [[ "$DETAILED" == "true" ]]; then
        generate_health_report
    fi
    
    local end_time duration
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    log_success "健康检查完成，耗时: ${duration}秒"
}

# 运行主函数
main "$@"
