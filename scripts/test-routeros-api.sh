#!/bin/bash

# Copyright 2025 New Vector Ltd
# SPDX-License-Identifier: AGPL-3.0-only

# RouterOS API 测试脚本

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 配置变量
TEST_DOMAIN="example.com"
SCRIPT_PATH="./dynamic-ip-manager.sh"

# 显示帮助信息
show_help() {
    cat << EOF
RouterOS API 测试脚本

用法: $0 [选项]

选项:
  -h, --help                  显示此帮助信息
  --routeros-host HOST        RouterOS设备IP地址
  --routeros-username USER    RouterOS用户名 (默认: admin)
  --routeros-password PASS    RouterOS密码
  --domain DOMAIN             测试域名 (默认: example.com)

环境变量:
  ROUTEROS_HOST               RouterOS设备IP地址
  ROUTEROS_USERNAME           RouterOS用户名
  ROUTEROS_PASSWORD           RouterOS密码

示例:
  $0 --routeros-host *********** --routeros-password mypassword
  
  # 使用环境变量
  export ROUTEROS_HOST="***********"
  export ROUTEROS_PASSWORD="mypassword"
  $0

EOF
}

# 参数解析
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --routeros-host)
            export ROUTEROS_HOST="$2"
            shift 2
            ;;
        --routeros-username)
            export ROUTEROS_USERNAME="$2"
            shift 2
            ;;
        --routeros-password)
            export ROUTEROS_PASSWORD="$2"
            shift 2
            ;;
        --domain)
            TEST_DOMAIN="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    local missing_deps=()
    
    if ! command -v curl >/dev/null 2>&1; then
        missing_deps+=("curl")
    fi
    
    if ! command -v timeout >/dev/null 2>&1; then
        missing_deps+=("timeout")
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "缺少依赖: ${missing_deps[*]}"
        return 1
    fi
    
    log_success "依赖检查通过"
    return 0
}

# 验证配置
verify_config() {
    log_info "验证测试配置..."
    
    if [[ -z "${ROUTEROS_HOST:-}" ]]; then
        log_error "缺少RouterOS主机地址，请设置 --routeros-host 或环境变量 ROUTEROS_HOST"
        return 1
    fi
    
    if [[ -z "${ROUTEROS_PASSWORD:-}" ]]; then
        log_error "缺少RouterOS密码，请设置 --routeros-password 或环境变量 ROUTEROS_PASSWORD"
        return 1
    fi
    
    if [[ ! -f "$SCRIPT_PATH" ]]; then
        log_error "找不到脚本文件: $SCRIPT_PATH"
        return 1
    fi
    
    log_success "配置验证通过"
    log_info "RouterOS主机: ${ROUTEROS_HOST}"
    log_info "RouterOS用户: ${ROUTEROS_USERNAME:-admin}"
    log_info "测试域名: $TEST_DOMAIN"
    
    return 0
}

# 测试1：RouterOS连接测试
test_routeros_connection() {
    log_info "=== 测试1：RouterOS连接测试 ==="
    
    local protocol="https"
    local port="${ROUTEROS_PORT:-443}"
    local username="${ROUTEROS_USERNAME:-admin}"
    local url="${protocol}://${ROUTEROS_HOST}:${port}/rest/system/identity"
    
    log_info "测试连接: $url"
    
    local response
    if response=$(timeout 10 curl -s -k -u "${username}:${ROUTEROS_PASSWORD}" "$url" 2>/dev/null); then
        if [[ -n "$response" ]]; then
            log_success "RouterOS连接测试成功"
            log_info "响应: $response"
            return 0
        else
            log_error "RouterOS连接测试失败：空响应"
            return 1
        fi
    else
        log_error "RouterOS连接测试失败：连接超时或失败"
        return 1
    fi
}

# 测试2：接口列表获取测试
test_interface_list() {
    log_info "=== 测试2：接口列表获取测试 ==="
    
    local protocol="https"
    local port="${ROUTEROS_PORT:-443}"
    local username="${ROUTEROS_USERNAME:-admin}"
    local url="${protocol}://${ROUTEROS_HOST}:${port}/rest/interface"
    
    log_info "获取接口列表: $url"
    
    local response
    if response=$(timeout 10 curl -s -k -u "${username}:${ROUTEROS_PASSWORD}" "$url" 2>/dev/null); then
        if [[ -n "$response" ]]; then
            log_success "接口列表获取成功"
            log_info "找到的接口:"
            echo "$response" | grep -o '"name":"[^"]*"' | sed 's/"name":"//g' | sed 's/"//g' | while read -r interface; do
                log_info "  - $interface"
            done
            return 0
        else
            log_error "接口列表获取失败：空响应"
            return 1
        fi
    else
        log_error "接口列表获取失败：连接超时或失败"
        return 1
    fi
}

# 测试3：IP地址获取测试
test_ip_address_list() {
    log_info "=== 测试3：IP地址获取测试 ==="
    
    local protocol="https"
    local port="${ROUTEROS_PORT:-443}"
    local username="${ROUTEROS_USERNAME:-admin}"
    local url="${protocol}://${ROUTEROS_HOST}:${port}/rest/ip/address"
    
    log_info "获取IP地址列表: $url"
    
    local response
    if response=$(timeout 10 curl -s -k -u "${username}:${ROUTEROS_PASSWORD}" "$url" 2>/dev/null); then
        if [[ -n "$response" ]]; then
            log_success "IP地址列表获取成功"
            log_info "找到的IP地址:"
            echo "$response" | grep -o '"address":"[^"]*"' | sed 's/"address":"//g' | sed 's/"//g' | while read -r address; do
                log_info "  - $address"
            done
            return 0
        else
            log_error "IP地址列表获取失败：空响应"
            return 1
        fi
    else
        log_error "IP地址列表获取失败：连接超时或失败"
        return 1
    fi
}

# 测试4：脚本IP检测功能测试
test_script_detection() {
    log_info "=== 测试4：脚本IP检测功能测试 ==="
    
    log_info "执行脚本IP检测..."
    local start_time=$(date +%s)
    
    if detected_ip=$(bash "$SCRIPT_PATH" detect --domain "$TEST_DOMAIN" 2>/dev/null); then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        log_success "脚本IP检测成功: $detected_ip"
        log_info "检测耗时: ${duration}秒"
        
        # 验证IP格式
        if [[ "$detected_ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            log_success "IP格式验证通过"
            return 0
        else
            log_error "IP格式验证失败: $detected_ip"
            return 1
        fi
    else
        log_error "脚本IP检测失败"
        return 1
    fi
}

# 生成测试报告
generate_report() {
    local report_file="routeros-api-test-report-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$report_file" << EOF
RouterOS API 测试报告
生成时间: $(date)

测试配置:
- RouterOS主机: ${ROUTEROS_HOST}
- RouterOS用户: ${ROUTEROS_USERNAME:-admin}
- 测试域名: $TEST_DOMAIN

测试结果:
- RouterOS连接测试: $(if test_routeros_connection >/dev/null 2>&1; then echo "通过"; else echo "失败"; fi)
- 接口列表获取测试: $(if test_interface_list >/dev/null 2>&1; then echo "通过"; else echo "失败"; fi)
- IP地址获取测试: $(if test_ip_address_list >/dev/null 2>&1; then echo "通过"; else echo "失败"; fi)
- 脚本IP检测测试: $(if test_script_detection >/dev/null 2>&1; then echo "通过"; else echo "失败"; fi)

建议:
- 如果连接测试失败，检查RouterOS设备网络连接和API服务状态
- 如果认证失败，检查用户名和密码是否正确
- 如果IP检测失败，检查WAN接口是否已连接并获得公网IP

EOF

    log_success "测试报告已生成: $report_file"
}

# 主函数
main() {
    log_info "开始RouterOS API功能测试..."
    
    # 检查依赖
    if ! check_dependencies; then
        exit 1
    fi
    
    # 验证配置
    if ! verify_config; then
        exit 1
    fi
    
    local failed_tests=0
    
    # 执行所有测试
    test_routeros_connection || ((failed_tests++))
    echo
    test_interface_list || ((failed_tests++))
    echo
    test_ip_address_list || ((failed_tests++))
    echo
    test_script_detection || ((failed_tests++))
    echo
    
    # 生成报告
    generate_report
    
    # 总结
    log_info "=== 测试总结 ==="
    if [[ $failed_tests -eq 0 ]]; then
        log_success "所有测试通过！RouterOS API功能工作正常。"
        exit 0
    else
        log_error "有 $failed_tests 个测试失败，请检查相关配置。"
        exit 1
    fi
}

# 运行主函数
main "$@"
