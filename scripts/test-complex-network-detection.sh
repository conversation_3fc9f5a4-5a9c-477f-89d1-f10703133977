#!/bin/bash

# Copyright 2025 New Vector Ltd
# SPDX-License-Identifier: AGPL-3.0-only

# 复杂网络环境IP检测测试脚本

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 测试配置
TEST_DOMAIN="${1:-example.com}"
SCRIPT_PATH="$(dirname "$0")/dynamic-ip-manager.sh"
TEST_RESULTS_DIR="test-results-$(date +%Y%m%d-%H%M%S)"

# 创建测试结果目录
mkdir -p "$TEST_RESULTS_DIR"

# 显示帮助信息
show_help() {
    cat << EOF
复杂网络环境IP检测测试脚本

用法: $0 [域名] [选项]

参数:
  域名    测试用的域名 (默认: example.com)

选项:
  -h, --help              显示此帮助信息
  --bypass-gateway        测试旁路网关模式
  --enterprise-network    测试企业网络环境
  --mobile-network        测试移动网络环境
  --cloud-nat             测试云环境NAT
  --all-scenarios         测试所有网络场景

测试场景:
1. 标准网络环境测试
2. 旁路网关环境测试
3. 企业网络环境测试
4. 移动网络环境测试
5. 云环境NAT测试
6. 受限网络环境测试
7. 网络故障模拟测试

EOF
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖工具..."
    
    local missing_tools=()
    local optional_tools=()
    
    # 必需工具
    for tool in dig ping curl jq; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            missing_tools+=("$tool")
        fi
    done
    
    # 可选工具
    for tool in traceroute stunclient mtr nc; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            optional_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "缺少必需工具: ${missing_tools[*]}"
        log_error "请安装缺少的工具后重试"
        return 1
    fi
    
    if [[ ${#optional_tools[@]} -gt 0 ]]; then
        log_warning "缺少可选工具: ${optional_tools[*]}"
        log_warning "某些测试功能可能不可用"
    fi
    
    log_success "依赖检查完成"
}

# 网络环境诊断
diagnose_network_environment() {
    log_info "=== 网络环境诊断 ==="
    
    local diag_file="$TEST_RESULTS_DIR/network-diagnosis.txt"
    
    {
        echo "网络环境诊断报告"
        echo "=================="
        echo "时间: $(date)"
        echo "主机: $(hostname)"
        echo ""
        
        echo "网络接口信息:"
        ip addr show 2>/dev/null || ifconfig 2>/dev/null || echo "无法获取网络接口信息"
        echo ""
        
        echo "路由表信息:"
        ip route show 2>/dev/null || route -n 2>/dev/null || echo "无法获取路由信息"
        echo ""
        
        echo "DNS配置:"
        cat /etc/resolv.conf 2>/dev/null || echo "无法读取DNS配置"
        echo ""
        
        echo "代理环境变量:"
        env | grep -i proxy || echo "未检测到代理配置"
        echo ""
        
        echo "网络连通性测试:"
        for host in ******* ******* ************; do
            if ping -c 1 -W 3 "$host" >/dev/null 2>&1; then
                echo "  $host: 可达"
            else
                echo "  $host: 不可达"
            fi
        done
        echo ""
        
        echo "DNS解析测试:"
        for dns in ******* ******* ************; do
            if dig +short google.com @"$dns" >/dev/null 2>&1; then
                echo "  $dns: DNS解析正常"
            else
                echo "  $dns: DNS解析失败"
            fi
        done
        
    } > "$diag_file"
    
    log_success "网络诊断完成，结果保存到: $diag_file"
}

# 测试标准网络环境
test_standard_network() {
    log_info "=== 测试标准网络环境 ==="
    
    local test_file="$TEST_RESULTS_DIR/standard-network-test.txt"
    local start_time=$(date +%s)
    
    log_info "执行标准IP检测..."
    if result=$(bash "$SCRIPT_PATH" detect --domain "$TEST_DOMAIN" 2>&1); then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        echo "标准网络环境测试结果" > "$test_file"
        echo "===================" >> "$test_file"
        echo "测试时间: $(date)" >> "$test_file"
        echo "检测耗时: ${duration}秒" >> "$test_file"
        echo "检测结果: $result" >> "$test_file"
        echo "" >> "$test_file"
        echo "详细输出:" >> "$test_file"
        echo "$result" >> "$test_file"
        
        log_success "标准网络测试通过，IP: $result，耗时: ${duration}秒"
    else
        log_error "标准网络测试失败"
        echo "标准网络环境测试失败" > "$test_file"
        echo "$result" >> "$test_file"
        return 1
    fi
}

# 测试旁路网关环境
test_bypass_gateway() {
    log_info "=== 测试旁路网关环境 ==="
    
    local test_file="$TEST_RESULTS_DIR/bypass-gateway-test.txt"
    
    log_info "启用旁路网关模式..."
    if result=$(bash "$SCRIPT_PATH" detect --domain "$TEST_DOMAIN" --bypass-gateway-mode enabled 2>&1); then
        echo "旁路网关环境测试结果" > "$test_file"
        echo "===================" >> "$test_file"
        echo "测试时间: $(date)" >> "$test_file"
        echo "检测结果: $result" >> "$test_file"
        
        log_success "旁路网关测试通过，IP: $result"
    else
        log_error "旁路网关测试失败"
        echo "旁路网关环境测试失败" > "$test_file"
        echo "$result" >> "$test_file"
        return 1
    fi
}

# 测试企业网络环境
test_enterprise_network() {
    log_info "=== 测试企业网络环境 ==="
    
    local test_file="$TEST_RESULTS_DIR/enterprise-network-test.txt"
    
    log_info "使用企业网络配置..."
    if result=$(bash "$SCRIPT_PATH" detect --domain "$TEST_DOMAIN" \
        --detection-methods "dns_query,dns_txt,interface" \
        --network-timeout 15 2>&1); then
        
        echo "企业网络环境测试结果" > "$test_file"
        echo "===================" >> "$test_file"
        echo "测试时间: $(date)" >> "$test_file"
        echo "检测结果: $result" >> "$test_file"
        
        log_success "企业网络测试通过，IP: $result"
    else
        log_error "企业网络测试失败"
        echo "企业网络环境测试失败" > "$test_file"
        echo "$result" >> "$test_file"
        return 1
    fi
}

# 测试移动网络环境
test_mobile_network() {
    log_info "=== 测试移动网络环境 ==="
    
    local test_file="$TEST_RESULTS_DIR/mobile-network-test.txt"
    
    log_info "使用移动网络配置..."
    if result=$(bash "$SCRIPT_PATH" detect --domain "$TEST_DOMAIN" \
        --detection-methods "stun,dns_txt,fallback" \
        --network-timeout 20 \
        --bypass-gateway-mode enabled 2>&1); then
        
        echo "移动网络环境测试结果" > "$test_file"
        echo "===================" >> "$test_file"
        echo "测试时间: $(date)" >> "$test_file"
        echo "检测结果: $result" >> "$test_file"
        
        log_success "移动网络测试通过，IP: $result"
    else
        log_error "移动网络测试失败"
        echo "移动网络环境测试失败" > "$test_file"
        echo "$result" >> "$test_file"
        return 1
    fi
}

# 测试云环境NAT
test_cloud_nat() {
    log_info "=== 测试云环境NAT ==="
    
    local test_file="$TEST_RESULTS_DIR/cloud-nat-test.txt"
    
    log_info "使用云NAT配置..."
    if result=$(bash "$SCRIPT_PATH" detect --domain "$TEST_DOMAIN" \
        --detection-methods "traceroute,dns_query,stun" \
        --network-timeout 10 2>&1); then
        
        echo "云环境NAT测试结果" > "$test_file"
        echo "=================" >> "$test_file"
        echo "测试时间: $(date)" >> "$test_file"
        echo "检测结果: $result" >> "$test_file"
        
        log_success "云NAT测试通过，IP: $result"
    else
        log_error "云NAT测试失败"
        echo "云环境NAT测试失败" > "$test_file"
        echo "$result" >> "$test_file"
        return 1
    fi
}

# 测试网络接口指定
test_interface_specification() {
    log_info "=== 测试网络接口指定 ==="
    
    local test_file="$TEST_RESULTS_DIR/interface-test.txt"
    
    # 获取可用的网络接口
    local interfaces
    interfaces=$(ip route | grep default | awk '{print $5}' | sort -u)
    
    echo "网络接口指定测试结果" > "$test_file"
    echo "===================" >> "$test_file"
    echo "测试时间: $(date)" >> "$test_file"
    echo "" >> "$test_file"
    
    for interface in $interfaces; do
        log_info "测试网络接口: $interface"
        
        if result=$(bash "$SCRIPT_PATH" detect --domain "$TEST_DOMAIN" \
            --preferred-interface "$interface" 2>&1); then
            
            echo "接口 $interface: 成功 - $result" >> "$test_file"
            log_success "接口 $interface 测试通过，IP: $result"
        else
            echo "接口 $interface: 失败" >> "$test_file"
            log_warning "接口 $interface 测试失败"
        fi
    done
}

# 性能压力测试
test_performance_stress() {
    log_info "=== 性能压力测试 ==="
    
    local test_file="$TEST_RESULTS_DIR/performance-stress-test.txt"
    local test_count=10
    local success_count=0
    local total_time=0
    
    echo "性能压力测试结果" > "$test_file"
    echo "================" >> "$test_file"
    echo "测试时间: $(date)" >> "$test_file"
    echo "测试次数: $test_count" >> "$test_file"
    echo "" >> "$test_file"
    
    for i in $(seq 1 $test_count); do
        log_info "第 $i 次性能测试..."
        local start_time=$(date +%s.%N)
        
        if result=$(bash "$SCRIPT_PATH" detect --domain "$TEST_DOMAIN" 2>/dev/null); then
            local end_time=$(date +%s.%N)
            local duration=$(echo "$end_time - $start_time" | bc -l)
            total_time=$(echo "$total_time + $duration" | bc -l)
            ((success_count++))
            
            echo "第 $i 次: 成功 - $result (${duration}秒)" >> "$test_file"
            log_success "第 $i 次测试成功，耗时: ${duration}秒"
        else
            echo "第 $i 次: 失败" >> "$test_file"
            log_error "第 $i 次测试失败"
        fi
        
        sleep 2
    done
    
    if [[ $success_count -gt 0 ]]; then
        local avg_time=$(echo "scale=3; $total_time / $success_count" | bc -l)
        echo "" >> "$test_file"
        echo "统计结果:" >> "$test_file"
        echo "成功率: $success_count/$test_count ($(echo "scale=1; $success_count * 100 / $test_count" | bc -l)%)" >> "$test_file"
        echo "平均耗时: ${avg_time}秒" >> "$test_file"
        
        log_success "性能测试完成，成功率: $success_count/$test_count，平均耗时: ${avg_time}秒"
    else
        log_error "所有性能测试都失败"
        return 1
    fi
}

# 生成测试报告
generate_test_report() {
    local report_file="$TEST_RESULTS_DIR/test-summary-report.txt"
    
    log_info "生成测试报告..."
    
    {
        echo "复杂网络环境IP检测测试报告"
        echo "=========================="
        echo "测试时间: $(date)"
        echo "测试域名: $TEST_DOMAIN"
        echo "脚本版本: $(grep "# 动态 IP 管理脚本" "$SCRIPT_PATH" || echo "未知")"
        echo ""
        
        echo "测试环境信息:"
        echo "- 操作系统: $(uname -s)"
        echo "- 内核版本: $(uname -r)"
        echo "- 主机名: $(hostname)"
        echo ""
        
        echo "测试结果汇总:"
        for test_file in "$TEST_RESULTS_DIR"/*.txt; do
            if [[ -f "$test_file" && "$test_file" != "$report_file" ]]; then
                local test_name=$(basename "$test_file" .txt)
                if grep -q "成功\|通过" "$test_file"; then
                    echo "✓ $test_name: 通过"
                else
                    echo "✗ $test_name: 失败"
                fi
            fi
        done
        
        echo ""
        echo "详细结果请查看各个测试文件。"
        
    } > "$report_file"
    
    log_success "测试报告已生成: $report_file"
}

# 主函数
main() {
    case "${1:-}" in
        -h|--help)
            show_help
            exit 0
            ;;
    esac
    
    log_info "开始复杂网络环境IP检测测试..."
    log_info "测试域名: $TEST_DOMAIN"
    log_info "结果目录: $TEST_RESULTS_DIR"
    
    # 检查依赖
    if ! check_dependencies; then
        exit 1
    fi
    
    # 检查脚本文件
    if [[ ! -f "$SCRIPT_PATH" ]]; then
        log_error "找不到脚本文件: $SCRIPT_PATH"
        exit 1
    fi
    
    local failed_tests=0
    
    # 执行测试
    diagnose_network_environment
    
    test_standard_network || ((failed_tests++))
    test_bypass_gateway || ((failed_tests++))
    test_enterprise_network || ((failed_tests++))
    test_mobile_network || ((failed_tests++))
    test_cloud_nat || ((failed_tests++))
    test_interface_specification || ((failed_tests++))
    test_performance_stress || ((failed_tests++))
    
    # 生成报告
    generate_test_report
    
    # 总结
    log_info "=== 测试总结 ==="
    if [[ $failed_tests -eq 0 ]]; then
        log_success "所有测试通过！复杂网络环境IP检测功能工作正常。"
        exit 0
    else
        log_error "有 $failed_tests 个测试失败，请检查相关功能。"
        exit 1
    fi
}

# 运行主函数
main "$@"
