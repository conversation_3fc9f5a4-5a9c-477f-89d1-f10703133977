# 网络环境配置文件
# Copyright 2025 New Vector Ltd
# SPDX-License-Identifier: AGPL-3.0-only

apiVersion: v1
kind: ConfigMap
metadata:
  name: network-environment-config
  namespace: matrix-external
  labels:
    app.kubernetes.io/name: dynamic-ip-manager
    app.kubernetes.io/component: network-config
data:
  # 网络接口配置
  preferred-interface: ""       # 首选网络接口，如: eth0, wlan0
  enable-interface-detection: "true"  # 启用自动接口检测
  interface-priority: |         # 网络接口优先级列表
    eth0
    ens3
    enp0s3
    wlan0
    wlp2s0

  # IP检测配置
  network-timeout: "10"         # 网络请求超时时间（秒）
  check-interval: "10"          # 检测间隔（秒）
  
  # IP检测服务配置
  ip-detection-services: |
    https://ping0.cc
    https://ipv4.icanhazip.com
    https://ifconfig.info

  # 备用HTTP服务配置
  fallback-http-services: |
    https://api.ipify.org
    https://checkip.amazonaws.com
    https://ipinfo.io/ip
  
  # 验证配置
  enable-ip-ownership-verification: "true"
  enable-ping-verification: "false"  # 简化验证，避免误判
  enable-service-verification: "true"
  
  # 日志配置
  log-level: "info"             # debug, info, warning, error
  enable-detailed-logging: "true"
  log-network-details: "true"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: network-troubleshooting-guide
  namespace: matrix-external
  labels:
    app.kubernetes.io/name: dynamic-ip-manager
    app.kubernetes.io/component: troubleshooting
data:
  common-issues.md: |
    # 网络环境常见问题排查指南
    
    ## 1. 旁路网关环境
    
    ### 症状
    - 不同IP检测服务返回不同结果
    - traceroute显示路径异常
    - HTTP请求被代理或重定向
    
    ### 解决方案
    ```bash
    # 启用旁路网关模式
    ./dynamic-ip-manager.sh detect --domain example.com --bypass-gateway-mode enabled
    
    # 指定特定网络接口
    ./dynamic-ip-manager.sh detect --domain example.com --preferred-interface eth0
    
    # 使用STUN协议绕过HTTP代理
    ./dynamic-ip-manager.sh detect --domain example.com --detection-methods stun,dns_txt
    ```
    
    ## 2. 企业网络环境
    
    ### 症状
    - 外部HTTP服务无法访问
    - DNS查询被拦截或重定向
    - 防火墙阻止某些协议
    
    ### 解决方案
    ```bash
    # 使用简化检测方法
    ./dynamic-ip-manager.sh detect --domain example.com

    # 指定网络接口
    ./dynamic-ip-manager.sh detect --domain example.com --preferred-interface eth0
    ```
    
    ## 3. 云环境NAT
    
    ### 症状
    - 检测到的IP是NAT网关IP
    - 服务验证失败
    - ping验证失败
    
    ### 解决方案
    ```bash
    # 使用标准检测方法
    ./dynamic-ip-manager.sh detect --domain example.com

    # 增加超时时间
    ./dynamic-ip-manager.sh detect --domain example.com --network-timeout 15
    ```
    
    ## 4. 移动网络环境
    
    ### 症状
    - IP频繁变化
    - 网络连接不稳定
    - 某些服务间歇性不可用
    
    ### 解决方案
    ```bash
    # 增加检测频率
    ./dynamic-ip-manager.sh monitor --domain example.com --check-interval 30
    
    # 增加超时时间适应移动网络
    ./dynamic-ip-manager.sh detect --domain example.com --network-timeout 20
    ```
  
  network-commands.md: |
    # 网络诊断命令参考
    
    ## 基本网络信息
    ```bash
    # 查看网络接口
    ip addr show
    ip link show
    
    # 查看路由表
    ip route show
    ip route get *******
    
    # 查看DNS配置
    cat /etc/resolv.conf
    systemd-resolve --status
    ```
    
    ## 连通性测试
    ```bash
    # ping测试
    ping -c 4 *******
    ping -c 4 *******
    
    # DNS解析测试
    nslookup google.com
    dig google.com
    dig @******* google.com
    
    # 端口连通性测试
    telnet ******* 53
    nc -zv ******* 53
    ```
    
    ## 路径分析
    ```bash
    # traceroute分析
    traceroute *******
    traceroute -n *******
    mtr *******
    
    # 路径MTU发现
    ping -M do -s 1472 *******
    ```
    
    ## 代理检测
    ```bash
    # 检查环境变量
    env | grep -i proxy
    
    # 检查HTTP代理
    curl -I http://httpbin.org/headers
    curl -I https://httpbin.org/headers
    
    # 检查透明代理
    curl -H "Host: example.com" http://*******/
    ```
    
    ## STUN测试
    ```bash
    # 安装STUN客户端
    apt-get install stun-client
    yum install stun
    
    # 测试STUN服务器
    stunclient stun.l.google.com
    stunclient stun.cloudflare.com 3478
    ```

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: network-optimization-profiles
  namespace: matrix-external
  labels:
    app.kubernetes.io/name: dynamic-ip-manager
    app.kubernetes.io/component: optimization
data:
  # 高速网络环境配置
  high-speed-network.env: |
    PREFERRED_INTERFACE=""
    NETWORK_TIMEOUT=5
    CHECK_INTERVAL=10
    ENABLE_INTERFACE_DETECTION=true

  # 企业网络环境配置
  enterprise-network.env: |
    PREFERRED_INTERFACE=""
    NETWORK_TIMEOUT=15
    CHECK_INTERVAL=30
    ENABLE_INTERFACE_DETECTION=true

  # 移动网络环境配置
  mobile-network.env: |
    PREFERRED_INTERFACE=""
    NETWORK_TIMEOUT=20
    CHECK_INTERVAL=60
    ENABLE_INTERFACE_DETECTION=true

  # 受限网络环境配置
  restricted-network.env: |
    PREFERRED_INTERFACE=""
    NETWORK_TIMEOUT=30
    CHECK_INTERVAL=120
    ENABLE_INTERFACE_DETECTION=false

  # 云环境NAT配置
  cloud-nat.env: |
    PREFERRED_INTERFACE=""
    NETWORK_TIMEOUT=10
    CHECK_INTERVAL=15
    ENABLE_INTERFACE_DETECTION=true
