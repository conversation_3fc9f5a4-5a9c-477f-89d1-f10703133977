#!/bin/bash

# Copyright 2025 New Vector Ltd
# SPDX-License-Identifier: AGPL-3.0-only

# 动态 IP 管理脚本

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
DOMAIN=""
INTERNAL_PORT="8443"
EXTERNAL_NAMESPACE="matrix-external"
INTERNAL_NAMESPACE="matrix-internal"
EXTERNAL_RELEASE="matrix-external"
INTERNAL_RELEASE="matrix-internal"
CONFIG_MAP_NAME="dynamic-ip-config"
CHECK_INTERVAL="${CHECK_INTERVAL:-5}"  # 改为5秒检测间隔

# RouterOS API 配置
ROUTEROS_HOST="${ROUTEROS_HOST:-}"                    # RouterOS设备IP地址
ROUTEROS_USERNAME="${ROUTEROS_USERNAME:-admin}"      # RouterOS用户名
ROUTEROS_PASSWORD="${ROUTEROS_PASSWORD:-}"           # RouterOS密码
ROUTEROS_PORT="${ROUTEROS_PORT:-443}"               # RouterOS REST API端口
ROUTEROS_USE_HTTPS="${ROUTEROS_USE_HTTPS:-true}"    # 是否使用HTTPS
ROUTEROS_WAN_INTERFACE="${ROUTEROS_WAN_INTERFACE:-}" # WAN接口名称（可选）
ROUTEROS_TIMEOUT="${ROUTEROS_TIMEOUT:-10}"          # API请求超时时间
ROUTEROS_RETRY_COUNT="${ROUTEROS_RETRY_COUNT:-3}"   # 重试次数
ROUTEROS_RETRY_DELAY="${ROUTEROS_RETRY_DELAY:-2}"   # 重试间隔

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示帮助信息
show_help() {
    cat << EOF
动态 IP 管理脚本 - RouterOS API 版本

用法: $0 [选项] <操作>

操作:
  detect          检测当前公网 IP
  update          更新 well-known 配置
  monitor         持续监控 IP 变化
  setup           初始化动态 IP 管理

必需参数:
  --domain DOMAIN              主域名 (如: example.com)
  --routeros-host HOST         RouterOS设备IP地址

RouterOS API 配置:
  --routeros-username USER     RouterOS用户名 (默认: admin)
  --routeros-password PASS     RouterOS密码 (建议使用环境变量)
  --routeros-port PORT         RouterOS REST API端口 (默认: 443)
  --routeros-https             启用HTTPS (默认启用)
  --routeros-interface IFACE   指定WAN接口名称 (可选，自动检测)
  --routeros-timeout SECONDS   API请求超时时间 (默认: 10)

可选参数:
  -h, --help                  显示此帮助信息
  --internal-port PORT        内部服务器端口 (默认: 8443)
  --external-namespace NS     外部服务器命名空间 (默认: matrix-external)
  --internal-namespace NS     内部服务器命名空间 (默认: matrix-internal)
  --check-interval SECONDS    检查间隔秒数 (默认: 5)

环境变量:
  ROUTEROS_HOST               RouterOS设备IP地址
  ROUTEROS_USERNAME           RouterOS用户名
  ROUTEROS_PASSWORD           RouterOS密码
  ROUTEROS_PORT               RouterOS REST API端口
  ROUTEROS_USE_HTTPS          是否使用HTTPS (true/false)
  ROUTEROS_WAN_INTERFACE      WAN接口名称
  ROUTEROS_TIMEOUT            API请求超时时间

示例:
  # 基本使用
  $0 detect --domain example.com --routeros-host ***********

  # 使用环境变量
  export ROUTEROS_HOST="***********"
  export ROUTEROS_PASSWORD="your-password"
  $0 monitor --domain example.com

  # 指定WAN接口
  $0 detect --domain example.com --routeros-host *********** --routeros-interface ether1

EOF
}

# 参数解析
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --domain)
            DOMAIN="$2"
            shift 2
            ;;
        --internal-port)
            INTERNAL_PORT="$2"
            shift 2
            ;;
        --external-namespace)
            EXTERNAL_NAMESPACE="$2"
            shift 2
            ;;
        --internal-namespace)
            INTERNAL_NAMESPACE="$2"
            shift 2
            ;;
        --check-interval)
            CHECK_INTERVAL="$2"
            shift 2
            ;;
        --routeros-host)
            ROUTEROS_HOST="$2"
            shift 2
            ;;
        --routeros-username)
            ROUTEROS_USERNAME="$2"
            shift 2
            ;;
        --routeros-password)
            ROUTEROS_PASSWORD="$2"
            shift 2
            ;;
        --routeros-port)
            ROUTEROS_PORT="$2"
            shift 2
            ;;
        --routeros-https)
            ROUTEROS_USE_HTTPS="true"
            shift
            ;;
        --routeros-interface)
            ROUTEROS_WAN_INTERFACE="$2"
            shift 2
            ;;
        --routeros-timeout)
            ROUTEROS_TIMEOUT="$2"
            shift 2
            ;;
        detect|update|monitor|setup)
            ACTION="$1"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证必需参数
if [[ -z "$DOMAIN" ]]; then
    log_error "缺少必需参数: --domain"
    show_help
    exit 1
fi

if [[ -z "${ACTION:-}" ]]; then
    log_error "缺少操作参数"
    show_help
    exit 1
fi

# 验证RouterOS配置
verify_routeros_config() {
    log_info "验证RouterOS配置..."

    if [[ -z "$ROUTEROS_HOST" ]]; then
        log_error "缺少RouterOS设备地址，请设置 --routeros-host 或环境变量 ROUTEROS_HOST"
        return 1
    fi

    if [[ -z "$ROUTEROS_PASSWORD" ]]; then
        log_error "缺少RouterOS密码，请设置 --routeros-password 或环境变量 ROUTEROS_PASSWORD"
        return 1
    fi

    # 验证主机地址格式
    if ! [[ "$ROUTEROS_HOST" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        log_error "RouterOS主机地址格式无效: $ROUTEROS_HOST"
        return 1
    fi

    # 验证端口号
    if ! [[ "$ROUTEROS_PORT" =~ ^[0-9]+$ ]] || [[ "$ROUTEROS_PORT" -lt 1 ]] || [[ "$ROUTEROS_PORT" -gt 65535 ]]; then
        log_error "RouterOS端口号无效: $ROUTEROS_PORT"
        return 1
    fi

    log_success "RouterOS配置验证通过"
    log_info "RouterOS主机: $ROUTEROS_HOST:$ROUTEROS_PORT"
    log_info "RouterOS用户: $ROUTEROS_USERNAME"
    log_info "使用HTTPS: $ROUTEROS_USE_HTTPS"

    return 0
}

# 测试RouterOS连接
test_routeros_connection() {
    log_info "测试RouterOS连接..."

    local protocol="http"
    if [[ "$ROUTEROS_USE_HTTPS" == "true" ]]; then
        protocol="https"
    fi

    local url="${protocol}://${ROUTEROS_HOST}:${ROUTEROS_PORT}/rest/system/identity"
    local auth="${ROUTEROS_USERNAME}:${ROUTEROS_PASSWORD}"

    local response
    response=$(timeout "$ROUTEROS_TIMEOUT" curl -s -k -u "$auth" "$url" 2>/dev/null)
    local curl_exit_code=$?

    if [[ $curl_exit_code -eq 0 && -n "$response" ]]; then
        log_success "RouterOS连接测试成功"
        return 0
    else
        case $curl_exit_code in
            28) log_error "RouterOS连接超时" ;;
            7) log_error "RouterOS连接失败，无法连接到主机" ;;
            22) log_error "RouterOS认证失败，请检查用户名和密码" ;;
            *) log_error "RouterOS连接失败，错误代码: $curl_exit_code" ;;
        esac
        return 1
    fi
}

# 获取RouterOS WAN接口列表
get_routeros_wan_interfaces() {
    log_info "获取RouterOS WAN接口列表..."

    local protocol="http"
    if [[ "$ROUTEROS_USE_HTTPS" == "true" ]]; then
        protocol="https"
    fi

    local url="${protocol}://${ROUTEROS_HOST}:${ROUTEROS_PORT}/rest/interface"
    local auth="${ROUTEROS_USERNAME}:${ROUTEROS_PASSWORD}"

    # 如果指定了WAN接口，直接返回
    if [[ -n "$ROUTEROS_WAN_INTERFACE" ]]; then
        log_info "使用指定的WAN接口: $ROUTEROS_WAN_INTERFACE"
        echo "$ROUTEROS_WAN_INTERFACE"
        return 0
    fi

    # 获取所有接口信息
    local interfaces_response
    interfaces_response=$(timeout "$ROUTEROS_TIMEOUT" curl -s -k -u "$auth" "$url" 2>/dev/null)

    if [[ $? -ne 0 || -z "$interfaces_response" ]]; then
        log_error "无法获取RouterOS接口列表"
        return 1
    fi

    # 解析接口信息，查找可能的WAN接口
    # 通常WAN接口名称包含: ether1, pppoe-out, wan, internet等
    local wan_interfaces=()

    # 使用简单的文本解析（避免依赖jq）
    while IFS= read -r line; do
        if [[ "$line" =~ \"name\":\"([^\"]+)\" ]]; then
            local interface_name="${BASH_REMATCH[1]}"
            # 检查是否为可能的WAN接口
            if [[ "$interface_name" =~ ^(ether1|pppoe-out|wan|internet|wlan1) ]]; then
                wan_interfaces+=("$interface_name")
            fi
        fi
    done <<< "$interfaces_response"

    if [[ ${#wan_interfaces[@]} -eq 0 ]]; then
        log_warning "未找到明显的WAN接口，尝试使用ether1作为默认接口"
        wan_interfaces=("ether1")
    fi

    log_info "找到可能的WAN接口: ${wan_interfaces[*]}"
    printf '%s\n' "${wan_interfaces[@]}"
}

# 获取RouterOS接口的IP地址
get_routeros_interface_ip() {
    local interface_name="$1"

    log_info "获取RouterOS接口 $interface_name 的IP地址..."

    local protocol="http"
    if [[ "$ROUTEROS_USE_HTTPS" == "true" ]]; then
        protocol="https"
    fi

    local url="${protocol}://${ROUTEROS_HOST}:${ROUTEROS_PORT}/rest/ip/address"
    local auth="${ROUTEROS_USERNAME}:${ROUTEROS_PASSWORD}"

    # 获取IP地址信息
    local ip_response
    ip_response=$(timeout "$ROUTEROS_TIMEOUT" curl -s -k -u "$auth" "$url" 2>/dev/null)

    if [[ $? -ne 0 || -z "$ip_response" ]]; then
        log_error "无法获取RouterOS IP地址信息"
        return 1
    fi

    # 解析IP地址信息，查找指定接口的公网IP
    local found_ips=()

    # 使用简单的文本解析
    while IFS= read -r line; do
        if [[ "$line" =~ \"interface\":\"$interface_name\" ]]; then
            # 在同一条记录中查找IP地址
            if [[ "$line" =~ \"address\":\"([0-9]+\.[0-9]+\.[0-9]+\.[0-9]+)/[0-9]+\" ]]; then
                local ip_addr="${BASH_REMATCH[1]}"
                # 验证是否为公网IP
                if is_public_ip "$ip_addr"; then
                    found_ips+=("$ip_addr")
                fi
            fi
        fi
    done <<< "$ip_response"

    if [[ ${#found_ips[@]} -eq 0 ]]; then
        log_warning "接口 $interface_name 未找到公网IP地址"
        return 1
    fi

    # 返回第一个找到的公网IP
    local detected_ip="${found_ips[0]}"
    log_success "接口 $interface_name 检测到公网IP: $detected_ip"
    echo "$detected_ip"
    return 0
}

# 检查IP是否为公网IP
is_public_ip() {
    local ip="$1"

    # 检查IP格式
    if ! [[ "$ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        return 1
    fi

    # 检查是否为私有IP或保留IP
    if [[ "$ip" =~ ^10\. ]] || \
       [[ "$ip" =~ ^192\.168\. ]] || \
       [[ "$ip" =~ ^172\.(1[6-9]|2[0-9]|3[01])\. ]] || \
       [[ "$ip" =~ ^127\. ]] || \
       [[ "$ip" =~ ^0\. ]] || \
       [[ "$ip" =~ ^169\.254\. ]] || \
       [[ "$ip" =~ ^224\. ]] || \
       [[ "$ip" =~ ^240\. ]]; then
        return 1
    fi

    return 0
}

# RouterOS API方式检测公网IP
detect_public_ip() {
    log_info "开始通过RouterOS API检测公网IP..."

    # 验证RouterOS配置
    if ! verify_routeros_config; then
        log_error "RouterOS配置验证失败"
        return 1
    fi

    local retry_count=0
    local detected_ip=""

    # 重试机制
    while [[ $retry_count -lt $ROUTEROS_RETRY_COUNT ]]; do
        if [[ $retry_count -gt 0 ]]; then
            log_info "第 $((retry_count + 1)) 次尝试..."
            sleep "$ROUTEROS_RETRY_DELAY"
        fi

        # 测试RouterOS连接
        if ! test_routeros_connection; then
            log_warning "RouterOS连接测试失败，尝试重试..."
            ((retry_count++))
            continue
        fi

        # 获取WAN接口列表
        local wan_interfaces
        mapfile -t wan_interfaces < <(get_routeros_wan_interfaces)

        if [[ ${#wan_interfaces[@]} -eq 0 ]]; then
            log_error "未找到WAN接口"
            ((retry_count++))
            continue
        fi

        # 尝试从每个WAN接口获取公网IP
        local interface_found=false
        for interface in "${wan_interfaces[@]}"; do
            log_info "检查WAN接口: $interface"

            if detected_ip=$(get_routeros_interface_ip "$interface"); then
                # 验证检测到的IP
                if verify_detected_ip "$detected_ip"; then
                    log_success "通过RouterOS API检测到公网IP: $detected_ip (接口: $interface)"
                    echo "$detected_ip"
                    return 0
                else
                    log_warning "接口 $interface 的IP验证失败: $detected_ip"
                fi
            else
                log_warning "接口 $interface 未获取到有效IP"
            fi
        done

        log_warning "所有WAN接口都未获取到有效的公网IP"
        ((retry_count++))
    done

    # 所有重试都失败
    log_error "RouterOS API检测公网IP失败，已重试 $ROUTEROS_RETRY_COUNT 次"
    log_error "故障排除建议："
    log_error "1. 检查RouterOS设备是否可达: ping $ROUTEROS_HOST"
    log_error "2. 检查RouterOS API服务是否启用"
    log_error "3. 检查用户名和密码是否正确"
    log_error "4. 检查WAN接口是否已连接并获得公网IP"
    log_error "5. 检查RouterOS防火墙设置"
    log_error "6. 尝试手动指定WAN接口: --routeros-interface ether1"
    return 1
}

# 验证检测到的IP
verify_detected_ip() {
    local ip_to_verify="$1"

    log_info "验证检测到的IP地址: $ip_to_verify"

    # 1. 使用公共函数进行基本验证
    if ! is_public_ip "$ip_to_verify"; then
        log_error "IP地址验证失败: $ip_to_verify (非公网IP)"
        return 1
    fi

    # 2. 基本可达性验证（可选，某些环境可能禁ping）
    log_info "执行IP可达性测试..."
    if timeout 3 ping -c 1 "$ip_to_verify" >/dev/null 2>&1; then
        log_success "IP可达性验证通过: $ip_to_verify"
    else
        log_info "IP可达性验证失败（可能被防火墙阻止，这是正常的）: $ip_to_verify"
        # 不返回失败，因为很多服务器禁ping
    fi

    # 3. 服务所有权验证（如果配置了域名）
    if [[ -n "${DOMAIN}" ]]; then
        log_info "执行IP所有权验证..."
        if timeout 8 curl -s -f --max-time 5 --connect-to "${DOMAIN}:443:${ip_to_verify}:443" \
           "https://${DOMAIN}/.well-known/matrix/server" >/dev/null 2>&1; then
            log_success "IP所有权验证通过: $ip_to_verify"
        else
            log_info "IP所有权验证失败（在IP变更期间这是正常的）: $ip_to_verify"
            # 不返回失败，因为在IP变更期间这个验证通常会失败
        fi
    fi

    log_success "IP地址验证完成: $ip_to_verify"
    return 0
}

# RouterOS API状态检查
check_routeros_status() {
    log_info "检查RouterOS设备状态..."

    if ! verify_routeros_config; then
        return 1
    fi

    if ! test_routeros_connection; then
        return 1
    fi

    log_info "RouterOS设备状态正常"
    return 0
}

# 获取当前存储的 IP
get_stored_ip() {
    kubectl get configmap "$CONFIG_MAP_NAME" -n "$EXTERNAL_NAMESPACE" -o jsonpath='{.data.current-ip}' 2>/dev/null || echo ""
}

# 存储当前 IP
store_ip() {
    local ip="$1"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # 创建或更新 ConfigMap
    kubectl create configmap "$CONFIG_MAP_NAME" \
        --from-literal=current-ip="$ip" \
        --from-literal=last-update="$timestamp" \
        --from-literal=domain="$DOMAIN" \
        --from-literal=internal-port="$INTERNAL_PORT" \
        -n "$EXTERNAL_NAMESPACE" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    log_success "IP 已存储: $ip (更新时间: $timestamp)"
}

# 更新 DNS 记录 - 使用 Cloudflare API
update_dns_records() {
    local new_ip="$1"

    log_info "更新 DNS 记录到新 IP: $new_ip"

    # 需要更新的子域名列表
    local subdomains=("matrix" "mas" "element" "rtc")

    # 检查是否有 Cloudflare API Token
    if [[ -z "${CLOUDFLARE_API_TOKEN:-}" ]]; then
        log_error "未设置 CLOUDFLARE_API_TOKEN 环境变量"
        log_error "请设置 Cloudflare API Token 以自动更新 DNS 记录"
        return 1
    fi

    # 获取 Zone ID（如果未设置）
    if [[ -z "${CLOUDFLARE_ZONE_ID:-}" ]]; then
        log_info "获取域名 $DOMAIN 的 Zone ID..."
        CLOUDFLARE_ZONE_ID=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones?name=$DOMAIN" \
            -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
            -H "Content-Type: application/json" | \
            jq -r '.result[0].id // empty')

        if [[ -z "$CLOUDFLARE_ZONE_ID" ]]; then
            log_error "无法获取域名 $DOMAIN 的 Zone ID"
            return 1
        fi
        log_success "获取到 Zone ID: $CLOUDFLARE_ZONE_ID"
    fi

    # 更新每个子域名的 DNS 记录
    for subdomain in "${subdomains[@]}"; do
        local full_domain="${subdomain}.${DOMAIN}"
        log_info "更新 DNS 记录: $full_domain -> $new_ip"

        # 获取现有记录 ID
        local record_id
        record_id=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/dns_records?name=$full_domain&type=A" \
            -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
            -H "Content-Type: application/json" | \
            jq -r '.result[0].id // empty')

        if [[ -n "$record_id" ]]; then
            # 更新现有记录
            local response
            response=$(curl -s -X PUT "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/dns_records/$record_id" \
                -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
                -H "Content-Type: application/json" \
                --data "{
                    \"type\": \"A\",
                    \"name\": \"$full_domain\",
                    \"content\": \"$new_ip\",
                    \"ttl\": 60
                }")

            if echo "$response" | jq -e '.success' >/dev/null; then
                log_success "DNS 记录更新成功: $full_domain"
            else
                log_error "DNS 记录更新失败: $full_domain"
                echo "$response" | jq '.errors' >&2
            fi
        else
            log_warning "未找到 DNS 记录: $full_domain，跳过更新"
        fi
    done
}

# 验证 DNS 记录更新
verify_dns_update() {
    local expected_ip="$1"

    log_info "验证 DNS 记录更新..."

    local subdomains=("matrix" "mas" "element" "rtc")
    local all_updated=true

    for subdomain in "${subdomains[@]}"; do
        local full_domain="${subdomain}.${DOMAIN}"
        local resolved_ip

        # 等待 DNS 传播
        sleep 10

        # 检查 DNS 解析结果
        resolved_ip=$(dig +short "$full_domain" @******* 2>/dev/null | head -n1 || echo "")

        if [[ "$resolved_ip" == "$expected_ip" ]]; then
            log_success "DNS 验证通过: $full_domain -> $resolved_ip"
        else
            log_error "DNS 验证失败: $full_domain -> $resolved_ip (期望: $expected_ip)"
            all_updated=false
        fi
    done

    if [[ "$all_updated" == "true" ]]; then
        log_success "所有 DNS 记录验证通过"
        return 0
    else
        log_error "部分 DNS 记录验证失败"
        return 1
    fi
}

# 验证服务可访问性
verify_service_accessibility() {
    local new_ip="$1"

    log_info "验证服务可访问性..."

    # 等待 DNS 传播完成
    sleep 30

    # 检查各个服务端点
    local services=(
        "matrix.${DOMAIN}:${INTERNAL_PORT}/_matrix/client/versions"
        "mas.${DOMAIN}:${INTERNAL_PORT}/health"
        "element.${DOMAIN}:${INTERNAL_PORT}/health"
        "rtc.${DOMAIN}:${INTERNAL_PORT}/health"
    )

    local all_accessible=true

    for service in "${services[@]}"; do
        local url="https://$service"
        log_info "检查服务: $url"

        if curl -s -f --max-time 10 "$url" >/dev/null 2>&1; then
            log_success "服务可访问: $service"
        else
            log_error "服务不可访问: $service"
            all_accessible=false
        fi
    done

    # 验证 well-known 委托（应该保持不变）
    local server_response
    server_response=$(curl -s --max-time 10 "https://${DOMAIN}/.well-known/matrix/server" 2>/dev/null || echo "")

    if [[ "$server_response" == *"matrix.${DOMAIN}:${INTERNAL_PORT}"* ]]; then
        log_success "well-known/server 委托正常"
    else
        log_error "well-known/server 委托异常"
        log_error "响应内容: $server_response"
        all_accessible=false
    fi

    if [[ "$all_accessible" == "true" ]]; then
        log_success "所有服务验证通过"
        return 0
    else
        log_error "部分服务验证失败"
        return 1
    fi
}

# 发送通知
send_notification() {
    local message="$1"
    local old_ip="$2"
    local new_ip="$3"
    
    # 这里可以集成各种通知方式
    log_info "发送通知: $message"
    
    # 示例：发送到 Slack/Discord/邮件等
    # curl -X POST "$WEBHOOK_URL" -d "{\"text\": \"$message\"}"
    
    # 记录到系统日志
    logger "Matrix Dynamic IP Update: $message (Old: $old_ip, New: $new_ip)"
}

# 初始化动态 IP 管理
setup_dynamic_ip() {
    log_info "初始化动态 IP 管理..."
    
    # 检测当前 IP
    local current_ip
    if current_ip=$(detect_public_ip); then
        log_success "检测到当前 IP: $current_ip"
        
        # 存储 IP
        store_ip "$current_ip"

        # 更新 DNS 记录
        if update_dns_records "$current_ip"; then
            verify_dns_update "$current_ip"
            verify_service_accessibility "$current_ip"
            log_success "动态 IP 管理初始化完成"
        else
            log_error "DNS 记录更新失败"
            return 1
        fi
    else
        log_error "IP 检测失败"
        return 1
    fi
}

# 执行 IP 更新
perform_ip_update() {
    local current_ip stored_ip
    
    # 检测当前 IP
    if ! current_ip=$(detect_public_ip); then
        log_error "IP 检测失败"
        return 1
    fi
    
    # 获取存储的 IP
    stored_ip=$(get_stored_ip)
    
    log_info "当前 IP: $current_ip"
    log_info "存储的 IP: $stored_ip"
    
    # 比较 IP 是否发生变化
    if [[ "$current_ip" != "$stored_ip" ]]; then
        log_warning "检测到 IP 变化: $stored_ip -> $current_ip"
        
        # 更新存储的 IP
        store_ip "$current_ip"

        # 更新 DNS 记录
        if update_dns_records "$current_ip"; then
            # 验证 DNS 更新
            if verify_dns_update "$current_ip"; then
                # 验证服务可访问性
                if verify_service_accessibility "$current_ip"; then
                    send_notification "IP 地址已更新并验证成功" "$stored_ip" "$current_ip"
                    log_success "IP 更新完成: $current_ip"
                else
                    send_notification "IP 地址更新后服务验证失败" "$stored_ip" "$current_ip"
                    log_error "服务可访问性验证失败"
                    return 1
                fi
            else
                send_notification "IP 地址更新后 DNS 验证失败" "$stored_ip" "$current_ip"
                log_error "DNS 验证失败"
                return 1
            fi
        else
            log_error "DNS 记录更新失败"
            return 1
        fi
    else
        log_info "IP 未发生变化，无需更新"
    fi
}

# 持续监控 IP 变化
monitor_ip_changes() {
    log_info "开始监控 IP 变化，检查间隔: ${CHECK_INTERVAL}秒"
    
    while true; do
        log_info "执行 IP 检查..."
        
        if perform_ip_update; then
            log_info "IP 检查完成"
        else
            log_error "IP 检查失败"
        fi
        
        log_info "等待 ${CHECK_INTERVAL}秒后进行下次检查..."
        sleep "$CHECK_INTERVAL"
    done
}

# 主函数
main() {
    # 在执行任何操作前，先验证RouterOS配置
    log_info "=== RouterOS API 动态IP管理脚本 ==="
    log_info "检测间隔: ${CHECK_INTERVAL}秒"

    case "$ACTION" in
        detect)
            log_info "执行IP检测操作..."
            if current_ip=$(detect_public_ip); then
                echo "$current_ip"
                log_success "当前公网IP检测完成: $current_ip"
            else
                log_error "IP检测失败"
                exit 1
            fi
            ;;
        update)
            log_info "执行IP更新操作..."
            if perform_ip_update; then
                log_success "IP更新操作完成"
            else
                log_error "IP更新操作失败"
                exit 1
            fi
            ;;
        monitor)
            log_info "启动IP监控模式..."
            log_info "使用RouterOS设备: $ROUTEROS_HOST:$ROUTEROS_PORT"
            log_info "监控域名: $DOMAIN"
            monitor_ip_changes
            ;;
        setup)
            log_info "执行初始化操作..."
            if setup_dynamic_ip; then
                log_success "初始化操作完成"
            else
                log_error "初始化操作失败"
                exit 1
            fi
            ;;
        *)
            log_error "未知操作: $ACTION"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main
