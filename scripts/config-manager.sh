#!/bin/bash

# Copyright 2025 New Vector Ltd
# SPDX-License-Identifier: AGPL-3.0-only

# 配置管理脚本

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_DIR="${PROJECT_ROOT}/deployment-configs"
SECRETS_DIR="${PROJECT_ROOT}/secrets"
TEMPLATES_DIR="${PROJECT_ROOT}/config-templates"

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示帮助信息
show_help() {
    cat << EOF
配置管理脚本

用法: $0 [选项] <操作>

操作:
  generate        生成配置文件
  validate        验证配置文件
  encrypt         加密敏感配置
  decrypt         解密敏感配置
  backup          备份配置
  restore         恢复配置

选项:
  -h, --help                  显示此帮助信息
  --env ENV                   环境名称 (staging/production)
  --domain DOMAIN             主域名
  --config-file FILE          配置文件路径
  --secrets-file FILE         敏感信息文件路径

示例:
  $0 generate --env production --domain example.com
  $0 validate --config-file config.yaml
  $0 encrypt --secrets-file secrets.yaml

EOF
}

# 创建目录结构
create_directories() {
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$SECRETS_DIR"
    mkdir -p "$TEMPLATES_DIR"
    mkdir -p "${PROJECT_ROOT}/backups"
}

# 生成配置文件模板
generate_config_template() {
    local env="$1"
    local domain="$2"
    local config_file="${CONFIG_DIR}/internal-server-${env}-${domain}.yaml"
    
    log_info "生成 $env 环境配置文件..."
    
    cat > "$config_file" << EOF
# 内部服务器配置 - $env 环境
# 域名: $domain
# 生成时间: $(date -u +"%Y-%m-%dT%H:%M:%SZ")

serverName: $domain

# 环境特定配置
environment: $env

# 证书管理
certManager:
  clusterIssuer: $([ "$env" = "production" ] && echo "cloudflare-letsencrypt" || echo "letsencrypt-staging")

# 全局 Ingress 配置
ingress:
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: $([ "$env" = "production" ] && echo "cloudflare-letsencrypt" || echo "letsencrypt-staging")
    nginx.ingress.kubernetes.io/proxy-body-size: 100M
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
  tlsEnabled: true

# 启用所有核心服务
initSecrets:
  enabled: true

deploymentMarkers:
  enabled: true

# Synapse 配置
synapse:
  enabled: true
  
  ingress:
    host: matrix.$domain
    annotations:
      kubernetes.io/ingress.class: nginx
      cert-manager.io/cluster-issuer: $([ "$env" = "production" ] && echo "cloudflare-letsencrypt" || echo "letsencrypt-staging")
      nginx.ingress.kubernetes.io/proxy-body-size: 100M
    tlsEnabled: true
  
  # 媒体存储配置
  media:
    storage:
      size: $([ "$env" = "production" ] && echo "100Gi" || echo "20Gi")
      storageClass: $([ "$env" = "production" ] && echo "fast-ssd" || echo "standard")
  
  # Worker 配置
  workers:
    client-reader:
      enabled: $([ "$env" = "production" ] && echo "true" || echo "false")
      replicas: $([ "$env" = "production" ] && echo "3" || echo "1")
    federation-inbound:
      enabled: $([ "$env" = "production" ] && echo "true" || echo "false")
      replicas: $([ "$env" = "production" ] && echo "2" || echo "1")
    media-repository:
      enabled: $([ "$env" = "production" ] && echo "true" || echo "false")
      replicas: $([ "$env" = "production" ] && echo "2" || echo "1")

# Matrix Authentication Service 配置
matrixAuthenticationService:
  enabled: true
  
  ingress:
    host: mas.$domain
    annotations:
      kubernetes.io/ingress.class: nginx
      cert-manager.io/cluster-issuer: $([ "$env" = "production" ] && echo "cloudflare-letsencrypt" || echo "letsencrypt-staging")
    tlsEnabled: true

# Matrix RTC 配置
matrixRTC:
  enabled: true
  
  ingress:
    host: rtc.$domain
    annotations:
      kubernetes.io/ingress.class: nginx
      cert-manager.io/cluster-issuer: $([ "$env" = "production" ] && echo "cloudflare-letsencrypt" || echo "letsencrypt-staging")
    tlsEnabled: true
  
  # SFU 配置
  sfu:
    enabled: true
    exposedServices:
      rtcTcp:
        enabled: true
        portType: NodePort
        port: $([ "$env" = "production" ] && echo "30881" || echo "30891")
      rtcMuxedUdp:
        enabled: true
        portType: NodePort
        port: $([ "$env" = "production" ] && echo "30882" || echo "30892")

# Element Web 配置
elementWeb:
  enabled: true
  
  ingress:
    host: element.$domain
    annotations:
      kubernetes.io/ingress.class: nginx
      cert-manager.io/cluster-issuer: $([ "$env" = "production" ] && echo "cloudflare-letsencrypt" || echo "letsencrypt-staging")
    tlsEnabled: true

# 数据库配置
postgres:
  enabled: $([ "$env" = "production" ] && echo "false" || echo "true")
  
  # 生产环境使用外部数据库
  $([ "$env" = "production" ] && cat << 'PROD_DB'
# 外部数据库配置（生产环境）
# 需要在 secrets 中配置实际的连接信息
PROD_DB
  )
  
  # 开发/测试环境使用内置数据库
  $([ "$env" != "production" ] && cat << 'DEV_DB'
storage:
    size: 20Gi
    storageClass: standard
DEV_DB
  )

# HAProxy 配置
haproxy:
  enabled: true

# 禁用 well-known 委托（由外部服务器处理）
wellKnownDelegation:
  enabled: false

# 资源配置
resources:
  synapse:
    requests:
      memory: $([ "$env" = "production" ] && echo "4Gi" || echo "1Gi")
      cpu: $([ "$env" = "production" ] && echo "2000m" || echo "500m")
    limits:
      memory: $([ "$env" = "production" ] && echo "8Gi" || echo "2Gi")
      cpu: $([ "$env" = "production" ] && echo "4000m" || echo "1000m")
  
  matrixAuthenticationService:
    requests:
      memory: $([ "$env" = "production" ] && echo "1Gi" || echo "512Mi")
      cpu: $([ "$env" = "production" ] && echo "1000m" || echo "250m")
    limits:
      memory: $([ "$env" = "production" ] && echo "2Gi" || echo "1Gi")
      cpu: $([ "$env" = "production" ] && echo "2000m" || echo "500m")
  
  postgres:
    requests:
      memory: $([ "$env" = "production" ] && echo "2Gi" || echo "512Mi")
      cpu: $([ "$env" = "production" ] && echo "1000m" || echo "250m")
    limits:
      memory: $([ "$env" = "production" ] && echo "4Gi" || echo "1Gi")
      cpu: $([ "$env" = "production" ] && echo "2000m" || echo "500m")

# 监控和日志配置
monitoring:
  enabled: $([ "$env" = "production" ] && echo "true" || echo "false")
  
logging:
  level: $([ "$env" = "production" ] && echo "INFO" || echo "DEBUG")

# 备份配置
backup:
  enabled: $([ "$env" = "production" ] && echo "true" || echo "false")
  schedule: "0 2 * * *"  # 每天凌晨2点
  retention: "30d"

EOF

    echo "$config_file"
}

# 生成敏感信息模板
generate_secrets_template() {
    local env="$1"
    local domain="$2"
    local secrets_file="${SECRETS_DIR}/secrets-${env}-${domain}.yaml"
    
    log_info "生成 $env 环境敏感信息模板..."
    
    cat > "$secrets_file" << EOF
# 敏感信息配置 - $env 环境
# 注意：此文件包含敏感信息，请妥善保管

# Cloudflare API Token
cloudflare:
  api_token: "CHANGE_ME_CLOUDFLARE_API_TOKEN"

# Let's Encrypt 邮箱
letsencrypt:
  email: "admin@$domain"

# 数据库配置（生产环境）
database:
  host: "CHANGE_ME_DB_HOST"
  port: 5432
  username: "CHANGE_ME_DB_USER"
  password: "CHANGE_ME_DB_PASSWORD"
  synapse_database: "synapse"
  mas_database: "mas"

# Matrix 配置
matrix:
  registration_shared_secret: "$(openssl rand -base64 32)"
  macaroon_secret_key: "$(openssl rand -base64 32)"
  form_secret: "$(openssl rand -base64 32)"

# MAS 配置
mas:
  secret_key: "$(openssl rand -base64 32)"
  encryption_key: "$(openssl rand -base64 32)"

# 通知配置
notifications:
  slack_webhook: "CHANGE_ME_SLACK_WEBHOOK_URL"
  email_smtp_host: "CHANGE_ME_SMTP_HOST"
  email_smtp_user: "CHANGE_ME_SMTP_USER"
  email_smtp_password: "CHANGE_ME_SMTP_PASSWORD"

EOF

    echo "$secrets_file"
}

# 验证配置文件
validate_config() {
    local config_file="$1"
    
    log_info "验证配置文件: $config_file"
    
    if [[ ! -f "$config_file" ]]; then
        log_error "配置文件不存在: $config_file"
        return 1
    fi
    
    # 检查 YAML 语法
    if ! python3 -c "import yaml; yaml.safe_load(open('$config_file'))" 2>/dev/null; then
        log_error "YAML 语法错误"
        return 1
    fi
    
    # 检查必需字段
    local required_fields=("serverName" "synapse.enabled" "matrixAuthenticationService.enabled")
    
    for field in "${required_fields[@]}"; do
        if ! yq eval ".$field" "$config_file" &>/dev/null; then
            log_error "缺少必需字段: $field"
            return 1
        fi
    done
    
    # 使用 Helm 模板验证
    if ! helm template test "$PROJECT_ROOT/charts/matrix-stack" -f "$config_file" --dry-run &>/dev/null; then
        log_error "Helm 模板验证失败"
        return 1
    fi
    
    log_success "配置文件验证通过"
}

# 加密敏感信息
encrypt_secrets() {
    local secrets_file="$1"
    local encrypted_file="${secrets_file}.enc"
    
    log_info "加密敏感信息文件..."
    
    if [[ ! -f "$secrets_file" ]]; then
        log_error "敏感信息文件不存在: $secrets_file"
        return 1
    fi
    
    # 使用 age 或 gpg 加密
    if command -v age &>/dev/null; then
        age -r "$(cat ~/.age/public_key 2>/dev/null || echo 'age1ql3z7hjy54pw3hyww5ayyfg7zqgvc7w3j2elw8zmrj2kg5sfn9aqmcac8p')" -o "$encrypted_file" "$secrets_file"
        log_success "文件已加密: $encrypted_file"
    elif command -v gpg &>/dev/null; then
        gpg --symmetric --cipher-algo AES256 --output "$encrypted_file" "$secrets_file"
        log_success "文件已加密: $encrypted_file"
    else
        log_error "未找到加密工具 (age 或 gpg)"
        return 1
    fi
    
    # 删除原始文件
    rm "$secrets_file"
    log_warning "原始文件已删除，请妥善保管加密文件"
}

# 解密敏感信息
decrypt_secrets() {
    local encrypted_file="$1"
    local secrets_file="${encrypted_file%.enc}"
    
    log_info "解密敏感信息文件..."
    
    if [[ ! -f "$encrypted_file" ]]; then
        log_error "加密文件不存在: $encrypted_file"
        return 1
    fi
    
    # 使用 age 或 gpg 解密
    if command -v age &>/dev/null && [[ "$encrypted_file" == *.age ]]; then
        age -d -i ~/.age/private_key -o "$secrets_file" "$encrypted_file"
        log_success "文件已解密: $secrets_file"
    elif command -v gpg &>/dev/null; then
        gpg --decrypt --output "$secrets_file" "$encrypted_file"
        log_success "文件已解密: $secrets_file"
    else
        log_error "未找到解密工具或密钥"
        return 1
    fi
}

# 备份配置
backup_config() {
    local env="$1"
    local backup_dir="${PROJECT_ROOT}/backups/config-backup-$(date +%Y%m%d-%H%M%S)"
    
    log_info "备份 $env 环境配置..."
    
    mkdir -p "$backup_dir"
    
    # 备份配置文件
    cp -r "$CONFIG_DIR" "$backup_dir/"
    
    # 备份加密的敏感信息
    if [[ -d "$SECRETS_DIR" ]]; then
        cp -r "$SECRETS_DIR" "$backup_dir/"
    fi
    
    # 创建备份清单
    cat > "$backup_dir/manifest.txt" << EOF
配置备份清单
备份时间: $(date)
环境: $env
备份内容:
- 配置文件目录: deployment-configs/
- 敏感信息目录: secrets/
EOF

    log_success "配置已备份到: $backup_dir"
}

# 主函数
main() {
    local action="$1"
    shift
    
    # 解析参数
    local env=""
    local domain=""
    local config_file=""
    local secrets_file=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --env)
                env="$2"
                shift 2
                ;;
            --domain)
                domain="$2"
                shift 2
                ;;
            --config-file)
                config_file="$2"
                shift 2
                ;;
            --secrets-file)
                secrets_file="$2"
                shift 2
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    create_directories
    
    case "$action" in
        generate)
            if [[ -z "$env" || -z "$domain" ]]; then
                log_error "generate 操作需要 --env 和 --domain 参数"
                exit 1
            fi
            
            config_file=$(generate_config_template "$env" "$domain")
            secrets_file=$(generate_secrets_template "$env" "$domain")
            
            log_success "配置文件已生成:"
            log_success "  配置文件: $config_file"
            log_success "  敏感信息: $secrets_file"
            log_warning "请编辑敏感信息文件并加密保存"
            ;;
        validate)
            if [[ -z "$config_file" ]]; then
                log_error "validate 操作需要 --config-file 参数"
                exit 1
            fi
            validate_config "$config_file"
            ;;
        encrypt)
            if [[ -z "$secrets_file" ]]; then
                log_error "encrypt 操作需要 --secrets-file 参数"
                exit 1
            fi
            encrypt_secrets "$secrets_file"
            ;;
        decrypt)
            if [[ -z "$secrets_file" ]]; then
                log_error "decrypt 操作需要 --secrets-file 参数"
                exit 1
            fi
            decrypt_secrets "$secrets_file"
            ;;
        backup)
            if [[ -z "$env" ]]; then
                log_error "backup 操作需要 --env 参数"
                exit 1
            fi
            backup_config "$env"
            ;;
        *)
            log_error "未知操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 检查参数
if [[ $# -eq 0 ]]; then
    show_help
    exit 1
fi

# 运行主函数
main "$@"
