# 内部服务器自动化部署方案

## 🎯 方案概述

基于技术验证报告，本文档提供了内部服务器的完整自动化部署方案，包括部署流程、配置管理、健康检查、故障恢复和动态 IP 处理。

## 📋 核心组件

### 1. 自动化部署脚本
- `scripts/deploy-internal-server.sh` - 主部署脚本
- `scripts/config-manager.sh` - 配置管理脚本
- `scripts/health-check-internal.sh` - 健康检查脚本
- `scripts/disaster-recovery.sh` - 故障恢复脚本
- `scripts/dynamic-ip-manager.sh` - 动态 IP 管理脚本

### 2. CI/CD 集成
- `.gitlab-ci-internal-server.yml` - GitLab CI/CD 配置
- 支持 staging 和 production 环境
- 自动化测试和验证流程

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd ess-helm

# 设置脚本权限
chmod +x scripts/*.sh

# 安装依赖
# - kubectl (>= 1.28.0)
# - helm (>= 3.12.0)
# - jq
# - curl
```

### 2. 生成配置文件

```bash
# 生成生产环境配置
./scripts/config-manager.sh generate \
  --env production \
  --domain example.com

# 编辑敏感信息
vim secrets/secrets-production-example.com.yaml

# 加密敏感信息
./scripts/config-manager.sh encrypt \
  --secrets-file secrets/secrets-production-example.com.yaml
```

### 3. 执行部署

```bash
# 部署内部服务器
./scripts/deploy-internal-server.sh \
  --domain example.com \
  --cloudflare-token "your-cloudflare-token" \
  --email "<EMAIL>" \
  --postgres-external \
  --postgres-host "db.example.com" \
  --postgres-user "matrix_user" \
  --postgres-password "secure_password"
```

### 4. 健康检查

```bash
# 执行健康检查
./scripts/health-check-internal.sh \
  --domain example.com \
  --detailed
```

## 📊 部署流程详解

### 阶段 1: 环境检查和准备

1. **依赖检查**
   - 验证 kubectl、helm、jq 等工具
   - 检查 Kubernetes 集群连接
   - 验证 cert-manager 安装状态

2. **环境验证**
   - 检查命名空间状态
   - 验证现有 Helm release
   - 确认资源配额和权限

3. **配置生成**
   - 根据环境和域名生成配置文件
   - 创建敏感信息模板
   - 验证配置文件语法

### 阶段 2: 依赖服务部署

1. **命名空间创建**
   ```bash
   kubectl create namespace matrix-internal
   ```

2. **敏感信息管理**
   ```bash
   # Cloudflare API Token
   kubectl create secret generic cloudflare-api-token \
     --from-literal=api-token="$CLOUDFLARE_TOKEN" \
     -n matrix-internal
   
   # 数据库密码（外部数据库）
   kubectl create secret generic postgres-credentials \
     --from-literal=synapse-password="$POSTGRES_PASSWORD" \
     --from-literal=mas-password="$POSTGRES_PASSWORD" \
     -n matrix-internal
   ```

3. **证书管理配置**
   ```yaml
   apiVersion: cert-manager.io/v1
   kind: ClusterIssuer
   metadata:
     name: cloudflare-letsencrypt
   spec:
     acme:
       server: https://acme-v02.api.letsencrypt.org/directory
       email: <EMAIL>
       privateKeySecretRef:
         name: cloudflare-letsencrypt
       solvers:
       - dns01:
           cloudflare:
             apiTokenSecretRef:
               name: cloudflare-api-token
               key: api-token
   ```

### 阶段 3: Matrix 服务部署

1. **Helm 部署**
   ```bash
   helm upgrade --install matrix-internal ./charts/matrix-stack \
     -f deployment-configs/internal-server-production-example.com.yaml \
     --namespace matrix-internal \
     --create-namespace \
     --wait \
     --timeout 20m
   ```

2. **服务组件**
   - Synapse (Matrix 主服务器)
   - Matrix Authentication Service (MAS)
   - Element Web (客户端)
   - Matrix RTC (WebRTC 服务)
   - HAProxy (负载均衡)
   - PostgreSQL (可选，内置或外部)

### 阶段 4: 部署后验证

1. **Pod 状态检查**
   ```bash
   kubectl get pods -n matrix-internal
   ```

2. **服务可用性验证**
   ```bash
   curl https://matrix.example.com:8443/_matrix/client/versions
   curl https://mas.example.com:8443/health
   curl https://element.example.com:8443/health
   ```

3. **证书状态验证**
   ```bash
   kubectl get certificates -n matrix-internal
   ```

## 🔧 配置管理

### 1. 环境特定配置

**Staging 环境特点**：
- 使用 Let's Encrypt Staging 证书
- 较小的资源配置
- 内置 PostgreSQL 数据库
- 简化的 Worker 配置

**Production 环境特点**：
- 使用 Cloudflare DNS-01 验证
- 高可用资源配置
- 外部 PostgreSQL 数据库
- 完整的 Worker 集群

### 2. 敏感信息管理

**安全最佳实践**：
- 使用 age 或 gpg 加密敏感文件
- 分离配置文件和敏感信息
- 使用 Kubernetes Secrets 存储
- 定期轮换密钥和令牌

**敏感信息类型**：
- Cloudflare API Token
- 数据库连接密码
- Matrix 共享密钥
- 通知服务凭据

### 3. 动态配置更新

```bash
# 更新配置
helm upgrade matrix-internal ./charts/matrix-stack \
  -f updated-config.yaml \
  --namespace matrix-internal

# 验证更新
./scripts/health-check-internal.sh --domain example.com
```

## 🏥 健康检查和监控

### 1. 自动化健康检查

**检查项目**：
- Pod 运行状态和就绪状态
- 服务端点可用性
- Ingress 配置和 DNS 解析
- 证书有效性和到期时间
- HTTPS 访问验证
- Matrix 功能验证

**检查频率**：
- 部署后立即检查
- 定时检查（每 5 分钟）
- 告警触发检查

### 2. 监控指标

**关键指标**：
- 服务可用性 (SLA: 99.9%)
- 响应时间 (< 500ms)
- 错误率 (< 0.1%)
- 资源使用率 (CPU < 80%, Memory < 85%)
- 证书到期时间 (> 30 天)

**告警规则**：
- 服务不可用 > 1 分钟
- 响应时间 > 2 秒
- 错误率 > 1%
- 证书到期 < 7 天

## 🚨 故障恢复

### 1. 自动恢复机制

**故障检测**：
- 健康检查失败
- Pod 异常退出
- 证书申请失败
- 存储空间不足

**自动恢复操作**：
- 重启异常服务
- 重新申请证书
- 清理临时文件
- 发送告警通知

### 2. 手动恢复流程

```bash
# 执行故障恢复
./scripts/disaster-recovery.sh auto-fix \
  --namespace matrix-internal \
  --alert-webhook "https://hooks.slack.com/..."

# 重启所有服务
./scripts/disaster-recovery.sh restart-all

# 修复证书问题
./scripts/disaster-recovery.sh fix-certs
```

### 3. 回滚机制

```bash
# 查看部署历史
helm history matrix-internal -n matrix-internal

# 回滚到上一个版本
helm rollback matrix-internal -n matrix-internal

# 验证回滚结果
./scripts/health-check-internal.sh --domain example.com
```

## 🌐 动态 IP 处理

### 1. IP 检测机制

**检测方法**：
- 主要：`curl --interface [网络接口] https://ping0.cc`
- 备用1：`curl --interface [网络接口] https://ipv4.icanhazip.com`
- 备用2：`curl --interface [网络接口] https://ifconfig.info`
- 标准方式：不指定接口的curl请求

**网络接口处理**：
- 自动检测可用的网络接口
- 支持手动指定首选网络接口
- 智能故障转移机制

**检测频率**：
- 默认：每 10 秒
- 可配置：5 秒 - 300 秒

### 2. 配置更新流程

```bash
# 初始化动态 IP 管理
./scripts/dynamic-ip-manager.sh setup --domain example.com

# 手动更新 IP
./scripts/dynamic-ip-manager.sh update --domain example.com

# 持续监控 IP 变化
./scripts/dynamic-ip-manager.sh monitor \
  --domain example.com \
  --check-interval 300
```

### 3. 高可用策略

**虚拟 IP + 路由方式**：
- IP 变化时只更新路由配置
- 不中断现有 TCP 连接
- 保持音视频通话稳定

**故障转移**：
- 多个 IP 检测源
- 配置更新失败重试
- 告警通知机制

## 🔄 CI/CD 集成

### 1. GitLab CI/CD 流水线

**阶段划分**：
1. **validate** - 配置验证
2. **build** - 配置生成
3. **deploy-staging** - Staging 部署
4. **test-staging** - Staging 测试
5. **deploy-production** - Production 部署（手动）
6. **test-production** - Production 测试

### 2. 环境变量配置

**必需变量**：
```bash
STAGING_DOMAIN=staging.example.com
PRODUCTION_DOMAIN=example.com
CLOUDFLARE_API_TOKEN=your-token
LETSENCRYPT_EMAIL=<EMAIL>
KUBECONFIG_STAGING=base64-encoded-kubeconfig
KUBECONFIG_PRODUCTION=base64-encoded-kubeconfig
```

**可选变量**：
```bash
POSTGRES_HOST=db.example.com
POSTGRES_USER=matrix_user
POSTGRES_PASSWORD=secure_password
SLACK_WEBHOOK=https://hooks.slack.com/...
```

### 3. 部署触发条件

**自动触发**：
- main 分支推送 → Staging 部署
- 定时任务 → 监控检查

**手动触发**：
- Production 部署
- 回滚操作
- 故障恢复

## 📈 性能优化

### 1. 资源配置优化

**生产环境推荐配置**：
```yaml
resources:
  synapse:
    requests: { memory: 4Gi, cpu: 2000m }
    limits: { memory: 8Gi, cpu: 4000m }
  
  matrixAuthenticationService:
    requests: { memory: 1Gi, cpu: 1000m }
    limits: { memory: 2Gi, cpu: 2000m }
```

### 2. 存储优化

**存储类型选择**：
- 生产环境：SSD 存储 (fast-ssd)
- 测试环境：标准存储 (standard)

**存储大小规划**：
- Synapse 媒体存储：100Gi+
- 数据库存储：50Gi+
- 日志存储：20Gi

### 3. 网络优化

**Ingress 配置**：
```yaml
annotations:
  nginx.ingress.kubernetes.io/proxy-body-size: 100M
  nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
  nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
  nginx.ingress.kubernetes.io/proxy-buffering: "off"
```

## 🔒 安全最佳实践

### 1. 网络安全

- 使用 TLS 1.3 加密
- 配置安全头部
- 限制网络访问范围
- 定期更新证书

### 2. 访问控制

- 使用 RBAC 权限控制
- 最小权限原则
- 定期审计访问日志
- 多因素认证

### 3. 数据保护

- 数据库加密
- 备份加密存储
- 敏感信息脱敏
- 定期安全扫描

## 📚 故障排除指南

### 1. 常见问题

**部署失败**：
- 检查资源配额
- 验证网络连接
- 确认权限设置

**证书问题**：
- 验证 DNS 配置
- 检查 API Token 权限
- 查看 cert-manager 日志

**服务不可用**：
- 检查 Pod 状态
- 验证 Ingress 配置
- 测试网络连通性

### 2. 日志查看

```bash
# 查看 Pod 日志
kubectl logs -f deployment/matrix-internal-synapse -n matrix-internal

# 查看事件
kubectl get events -n matrix-internal --sort-by='.lastTimestamp'

# 查看 Helm 状态
helm status matrix-internal -n matrix-internal
```

## 🎯 总结

这个自动化部署方案提供了：

✅ **完整的部署流程** - 从环境检查到服务验证
✅ **安全的配置管理** - 敏感信息加密和分离
✅ **可靠的健康检查** - 多维度监控和验证
✅ **自动的故障恢复** - 智能检测和修复
✅ **灵活的动态 IP 处理** - 高可用的 IP 变化应对
✅ **完善的 CI/CD 集成** - 自动化测试和部署

通过这套方案，您可以实现内部服务器的完全自动化部署和运维，确保服务的高可用性和稳定性。
