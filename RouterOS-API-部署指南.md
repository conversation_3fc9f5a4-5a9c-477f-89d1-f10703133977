# RouterOS API 动态IP检测部署指南

## 📋 概述

本文档详细说明了如何部署和配置基于RouterOS API的动态IP检测系统。该系统完全替换了原有的HTTP服务检测方法，通过RouterOS设备的REST API直接获取WAN接口的公网IP地址。

## 🎯 主要特性

- **RouterOS API集成**: 直接通过RouterOS REST API获取公网IP
- **高频检测**: 5秒检测间隔，快速响应IP变化
- **自动重试**: 内置重试机制，提高可靠性
- **中文日志**: 完整的中文日志记录
- **安全认证**: 支持HTTPS和用户认证
- **自动接口检测**: 智能识别WAN接口

## 🔧 系统要求

### RouterOS设备要求

1. **RouterOS版本**: 6.45或更高版本（支持REST API）
2. **API服务**: 启用REST API服务
3. **用户权限**: API用户需要具有读取权限
4. **网络连接**: 设备可通过网络访问

### 服务器要求

1. **操作系统**: Linux (支持bash)
2. **依赖软件**: curl, timeout, jq (可选)
3. **网络访问**: 能够访问RouterOS设备
4. **Kubernetes**: 如果使用容器部署

## 📦 安装步骤

### 1. 准备RouterOS设备

#### 启用REST API服务

```bash
# 通过RouterOS CLI启用REST API
/ip service enable api-ssl
/ip service set api-ssl port=443

# 或者通过WebFig界面
# IP -> Services -> api-ssl -> Enable
```

#### 创建API用户

```bash
# 创建专用的API用户
/user add name=api-user password=your-secure-password group=read

# 或者使用现有的admin用户（不推荐生产环境）
```

#### 验证API访问

```bash
# 测试API连接
curl -k -u "api-user:your-password" \
  "https://***********:443/rest/system/identity"
```

### 2. 配置脚本

#### 基本配置

```bash
# 设置环境变量
export ROUTEROS_HOST="***********"
export ROUTEROS_USERNAME="api-user"
export ROUTEROS_PASSWORD="your-secure-password"
export ROUTEROS_PORT="443"
export ROUTEROS_USE_HTTPS="true"

# 设置域名和其他参数
export DOMAIN="example.com"
export CHECK_INTERVAL="5"
```

#### 高级配置

```bash
# 指定WAN接口（可选）
export ROUTEROS_WAN_INTERFACE="ether1"

# 调整超时和重试参数
export ROUTEROS_TIMEOUT="10"
export ROUTEROS_RETRY_COUNT="3"
export ROUTEROS_RETRY_DELAY="2"
```

### 3. 测试部署

#### 运行测试脚本

```bash
# 基本连接测试
./scripts/test-routeros-api.sh \
  --routeros-host *********** \
  --routeros-password your-password

# 或使用环境变量
export ROUTEROS_HOST="***********"
export ROUTEROS_PASSWORD="your-password"
./scripts/test-routeros-api.sh
```

#### 测试IP检测功能

```bash
# 单次IP检测
./scripts/dynamic-ip-manager.sh detect \
  --domain example.com \
  --routeros-host ***********

# 执行一次更新
./scripts/dynamic-ip-manager.sh update \
  --domain example.com \
  --routeros-host ***********
```

### 4. 生产部署

#### 使用systemd服务

```bash
# 创建服务文件
sudo tee /etc/systemd/system/routeros-dynamic-ip.service << EOF
[Unit]
Description=RouterOS Dynamic IP Manager
After=network.target

[Service]
Type=simple
User=nobody
Environment=ROUTEROS_HOST=***********
Environment=ROUTEROS_USERNAME=api-user
Environment=ROUTEROS_PASSWORD=your-password
Environment=DOMAIN=example.com
Environment=CHECK_INTERVAL=5
ExecStart=/opt/scripts/dynamic-ip-manager.sh monitor --domain example.com
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable routeros-dynamic-ip
sudo systemctl start routeros-dynamic-ip
```

#### 使用Kubernetes部署

```bash
# 应用配置
kubectl apply -f scripts/routeros-config-example.yaml

# 检查部署状态
kubectl get pods -n matrix-external
kubectl logs -f deployment/routeros-dynamic-ip-manager -n matrix-external
```

## 🔍 配置参数详解

### RouterOS API 参数

| 参数 | 环境变量 | 默认值 | 说明 |
|------|----------|--------|------|
| `--routeros-host` | `ROUTEROS_HOST` | 无 | RouterOS设备IP地址 |
| `--routeros-username` | `ROUTEROS_USERNAME` | admin | RouterOS用户名 |
| `--routeros-password` | `ROUTEROS_PASSWORD` | 无 | RouterOS密码 |
| `--routeros-port` | `ROUTEROS_PORT` | 443 | REST API端口 |
| `--routeros-https` | `ROUTEROS_USE_HTTPS` | true | 是否使用HTTPS |
| `--routeros-interface` | `ROUTEROS_WAN_INTERFACE` | 自动检测 | WAN接口名称 |
| `--routeros-timeout` | `ROUTEROS_TIMEOUT` | 10 | API请求超时（秒） |

### 检测参数

| 参数 | 环境变量 | 默认值 | 说明 |
|------|----------|--------|------|
| `--check-interval` | `CHECK_INTERVAL` | 5 | 检测间隔（秒） |
| `--domain` | `DOMAIN` | 无 | 主域名 |
| `--internal-port` | `INTERNAL_PORT` | 8443 | 内部服务端口 |

## 🚨 故障排除

### 常见问题

#### 1. RouterOS连接失败

```bash
# 检查网络连接
ping ***********

# 检查API服务状态
curl -k https://***********:443/rest/system/identity

# 检查防火墙规则
# 确保允许443端口访问
```

#### 2. 认证失败

```bash
# 验证用户名密码
# 检查用户权限
# 确保用户属于正确的用户组
```

#### 3. 无法获取公网IP

```bash
# 检查WAN接口状态
# 确保接口已连接并获得IP
# 检查接口名称是否正确
```

### 调试模式

```bash
# 启用详细日志
export LOG_LEVEL="debug"

# 手动执行检测
./scripts/dynamic-ip-manager.sh detect \
  --domain example.com \
  --routeros-host *********** 2>&1 | tee debug.log

# 分析日志
grep -E "(ERROR|WARNING)" debug.log
```

## 📊 性能优化

### 检测频率调优

```bash
# 高频检测（适用于不稳定网络）
CHECK_INTERVAL=3

# 标准检测（推荐）
CHECK_INTERVAL=5

# 低频检测（适用于稳定网络）
CHECK_INTERVAL=10
```

### 超时参数调优

```bash
# 快速网络环境
ROUTEROS_TIMEOUT=5

# 标准网络环境
ROUTEROS_TIMEOUT=10

# 慢速网络环境
ROUTEROS_TIMEOUT=15
```

## 🔒 安全建议

1. **使用专用API用户**: 不要使用admin用户
2. **最小权限原则**: 只授予必要的读取权限
3. **强密码策略**: 使用复杂密码
4. **HTTPS加密**: 始终使用HTTPS连接
5. **网络隔离**: 限制API访问来源IP
6. **定期更新**: 保持RouterOS系统更新

## 📈 监控和告警

### 日志监控

```bash
# 监控关键日志
tail -f /var/log/routeros-dynamic-ip.log | grep -E "(ERROR|SUCCESS)"

# 设置日志轮转
logrotate /etc/logrotate.d/routeros-dynamic-ip
```

### 健康检查

```bash
# 定期健康检查
*/5 * * * * /opt/scripts/dynamic-ip-manager.sh detect --domain example.com >/dev/null || echo "IP detection failed" | mail -s "Alert" <EMAIL>
```

## 🔄 升级和迁移

### 从HTTP检测方式迁移

1. 备份现有配置
2. 更新脚本文件
3. 修改配置参数
4. 测试新功能
5. 切换到新系统
6. 监控运行状态

### 版本升级

1. 停止现有服务
2. 备份配置文件
3. 更新脚本文件
4. 验证配置兼容性
5. 重启服务
6. 验证功能正常

## 📞 技术支持

如果遇到问题，请：

1. 查看日志文件
2. 运行测试脚本
3. 检查网络连接
4. 验证RouterOS配置
5. 联系技术支持
