# 简化IP检测方案的values.yaml配置示例
# Copyright 2025 New Vector Ltd
# SPDX-License-Identifier: AGPL-3.0-only

# 动态IP更新器配置
dynamicIpUpdater:
  enabled: true
  
  # IP检测配置
  ipDetection:
    # 检测调度 (每10秒检测一次)
    schedule: "*/10 * * * * *"
    
    # 首选网络接口 (可选)
    preferredInterface: ""  # 例如: "eth0", "ens3"
    
    # 启用自动接口检测
    enableInterfaceDetection: true
    
    # 网络超时设置 (秒)
    networkTimeout: 10
    
    # IP检测服务列表 (按优先级排序)
    services:
      primary: "https://ping0.cc"
      fallback:
        - "https://ipv4.icanhazip.com"
        - "https://ifconfig.info"
        - "https://api.ipify.org"
        - "https://checkip.amazonaws.com"
    
    # 验证配置
    verification:
      enablePingVerification: false  # 禁用ping验证避免误判
      enableOwnershipVerification: true
      enableServiceVerification: true
  
  # Cloudflare API配置
  cloudflare:
    # API Token (通过Secret提供)
    apiTokenSecret:
      name: "cloudflare-api-credentials"
      key: "api-token"
    
    # Zone ID (可选，脚本会自动获取)
    zoneIdSecret:
      name: "cloudflare-api-credentials"
      key: "zone-id"
    
    # DNS记录TTL
    dnsRecordTtl: 60
  
  # 资源配置
  resources:
    requests:
      memory: "64Mi"
      cpu: "50m"
    limits:
      memory: "128Mi"
      cpu: "100m"
  
  # 安全上下文
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    runAsGroup: 1000
    capabilities:
      add:
        - NET_ADMIN  # 需要网络管理权限用于接口检测
  
  # 服务账户配置
  serviceAccount:
    create: true
    name: ""
    annotations: {}
  
  # 日志配置
  logging:
    level: "info"  # debug, info, warning, error
    enableDetailedLogging: true
  
  # 监控配置
  monitoring:
    enabled: true
    serviceMonitor:
      enabled: false
    prometheusRule:
      enabled: false

# 网络策略配置 (可选)
networkPolicy:
  enabled: false
  ingress: []
  egress:
    # 允许访问IP检测服务
    - to: []
      ports:
      - protocol: TCP
        port: 443
      - protocol: TCP
        port: 80
    # 允许DNS查询
    - to: []
      ports:
      - protocol: UDP
        port: 53
      - protocol: TCP
        port: 53

# 示例环境变量配置
env:
  # 基础配置
  DOMAIN: "example.com"
  CHECK_INTERVAL: "10"
  NETWORK_TIMEOUT: "10"
  
  # 网络接口配置
  PREFERRED_INTERFACE: ""
  ENABLE_INTERFACE_DETECTION: "true"
  
  # 验证配置
  ENABLE_IP_OWNERSHIP_VERIFICATION: "true"
  ENABLE_PING_VERIFICATION: "false"
  ENABLE_SERVICE_VERIFICATION: "true"
  
  # 日志配置
  LOG_LEVEL: "info"
  ENABLE_DETAILED_LOGGING: "true"

# 配置映射
configMaps:
  # 网络环境配置
  networkConfig:
    name: "dynamic-ip-network-config"
    data:
      # IP检测服务配置
      ip-detection-services: |
        https://ping0.cc
        https://ipv4.icanhazip.com
        https://ifconfig.info
      
      # 备用服务配置
      fallback-services: |
        https://api.ipify.org
        https://checkip.amazonaws.com
        https://ipinfo.io/ip
      
      # 网络接口优先级
      interface-priority: |
        eth0
        ens3
        enp0s3
        wlan0
        wlp2s0
      
      # 超时配置
      network-timeout: "10"
      check-interval: "10"
      
      # 验证配置
      enable-ip-ownership-verification: "true"
      enable-ping-verification: "false"
      enable-service-verification: "true"

# 密钥配置示例
secrets:
  cloudflareCredentials:
    name: "cloudflare-api-credentials"
    type: "Opaque"
    data:
      # 这些值需要base64编码
      # api-token: "your-cloudflare-api-token-base64"
      # zone-id: "your-cloudflare-zone-id-base64"

# RBAC配置
rbac:
  create: true
  rules:
    - apiGroups: [""]
      resources: ["configmaps"]
      verbs: ["get", "list", "create", "update", "patch"]
    - apiGroups: [""]
      resources: ["secrets"]
      verbs: ["get", "list"]
    - apiGroups: [""]
      resources: ["pods"]
      verbs: ["get", "list"]

# 持久化配置 (可选)
persistence:
  enabled: false
  storageClass: ""
  accessMode: ReadWriteOnce
  size: 1Gi
  annotations: {}

# 节点选择器 (可选)
nodeSelector: {}

# 容忍度配置 (可选)
tolerations: []

# 亲和性配置 (可选)
affinity: {}

# Pod安全策略 (可选)
podSecurityPolicy:
  enabled: false

# 优先级类 (可选)
priorityClassName: ""

# 镜像拉取密钥 (可选)
imagePullSecrets: []

# 注解配置
annotations:
  deployment: {}
  pod: {}
  service: {}

# 标签配置
labels:
  deployment: {}
  pod: {}
  service: {}

# 健康检查配置
healthCheck:
  livenessProbe:
    enabled: true
    initialDelaySeconds: 30
    periodSeconds: 60
    timeoutSeconds: 10
    failureThreshold: 3
  
  readinessProbe:
    enabled: true
    initialDelaySeconds: 10
    periodSeconds: 30
    timeoutSeconds: 10
    failureThreshold: 3

# 自动扩缩容配置 (通常不需要)
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80
