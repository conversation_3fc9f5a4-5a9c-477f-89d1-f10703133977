{{- /*
Copyright 2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- $root := .root -}}
{{- with required "well-known/nginx.conf.tpl missing context" .context -}}

# Nginx configuration for well-known delegation service
server {
    listen 8080;
    listen [::]:8080;
    server_name _;
    
    # Security headers
    add_header X-Frame-Options SAMEORIGIN always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Content-Security-Policy "frame-ancestors 'self'" always;
    add_header X-Robots-Tag "noindex, nofollow, noarchive, noimageindex" always;
    
    # CORS headers for Matrix well-known
    add_header Access-Control-Allow-Origin * always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "X-Requested-With, Content-Type, Authorization" always;
    
    # Deny non-GET methods for well-known endpoints
    if ($request_method !~ ^(GET|HEAD|OPTIONS)$) {
        return 405;
    }
    
    # Handle OPTIONS requests
    if ($request_method = OPTIONS) {
        return 204;
    }
    
    # Matrix server well-known
    location = /.well-known/matrix/server {
        default_type application/json;
        return 200 '{{ include "element-io.well-known-delegation.server" (dict "root" $root "context" .) | replace "'" "\\'" }}';
    }
    
    # Matrix client well-known
    location = /.well-known/matrix/client {
        default_type application/json;
        return 200 '{{ include "element-io.well-known-delegation.client" (dict "root" $root "context" .) | replace "'" "\\'" }}';
    }
    
    # Matrix support well-known
    location = /.well-known/matrix/support {
        default_type application/json;
        return 200 '{{ include "element-io.well-known-delegation.support" (dict "root" $root "context" .) | replace "'" "\\'" }}';
    }
    
    # Element well-known
    location = /.well-known/element/element.json {
        default_type application/json;
        return 200 '{{ include "element-io.well-known-delegation.element" (dict "root" $root "context" .) | replace "'" "\\'" }}';
    }
    
{{- if and .baseDomainRedirect.enabled (or $root.Values.elementWeb.enabled .baseDomainRedirect.url) }}
    # Base domain redirect
    location / {
{{- if $root.Values.elementWeb.enabled }}
{{- with $root.Values.elementWeb }}
{{- $elementWebHttps := include "element-io.ess-library.ingress.tlsHostsSecret" (dict "root" $root "context" (dict "hosts" (list .ingress.host) "tlsSecret" .ingress.tlsSecret "ingressName" "element-web")) }}
        return 301 http{{ if $elementWebHttps }}s{{ end }}://{{ tpl .ingress.host $root }};
{{- end }}
{{- else if .baseDomainRedirect.url }}
        return 301 {{ .baseDomainRedirect.url }};
{{- end }}
    }
{{- else }}
    # Return 404 for other paths
    location / {
        return 404;
    }
{{- end }}
    
    # Health check endpoint
    location = /health {
        access_log off;
        default_type application/json;
        return 200 '{"status": "ok"}';
    }
    
    # Disable access logs for well-known requests to reduce noise
    location ~* ^/.well-known/ {
        access_log off;
    }
}

{{- end -}}
