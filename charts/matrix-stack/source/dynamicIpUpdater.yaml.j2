{{- /*
Copyright 2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- $root := .root -}}
{{- with required "dynamicIpUpdater.yaml.j2 missing context" .context -}}

{{- if .enabled -}}

# ConfigMap to store current IP address
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $root.Release.Name }}-dynamic-ip-config
  namespace: {{ $root.Release.Namespace }}
  labels:
    {{- include "element-io.dynamic-ip-updater.labels" (dict "root" $root "context" .) | nindent 4 }}
data:
  current-ip: ""
  last-update: ""

---

# CronJob for IP detection and update
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ $root.Release.Name }}-dynamic-ip-updater
  namespace: {{ $root.Release.Namespace }}
  labels:
    {{- include "element-io.dynamic-ip-updater.labels" (dict "root" $root "context" .) | nindent 4 }}
spec:
  schedule: {{ .ipDetection.schedule | quote }}
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            {{- include "element-io.dynamic-ip-updater.labels" (dict "root" $root "context" .) | nindent 12 }}
        spec:
          restartPolicy: OnFailure
          serviceAccountName: {{ $root.Release.Name }}-dynamic-ip-updater
          containers:
          - name: ip-updater
            image: {{ include "element-io.ess-library.image.name" (dict "root" $root "context" $root.Values.matrixTools) }}
            imagePullPolicy: {{ include "element-io.ess-library.image.pullPolicy" (dict "root" $root "context" $root.Values.matrixTools) }}
            command:
            - /bin/bash
            - -c
            - |
              set -euo pipefail

              # 简化的IP检测函数
              detect_ip_with_interface() {
                local interface="$1"
                local service_url="$2"
                local service_name="$3"

                echo "Detecting IP using interface $interface and service $service_name" >&2
                timeout 10 curl -s --interface "$interface" --max-time 5 "$service_url" 2>/dev/null | grep -oE '^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$' | head -1 || echo ""
              }

              detect_ip_standard() {
                local service_url="$1"
                local service_name="$2"

                echo "Detecting IP using standard method and service $service_name" >&2
                timeout 10 curl -s --max-time 5 "$service_url" 2>/dev/null | grep -oE '^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$' | head -1 || echo ""
              }

              # 获取可用网络接口
              get_available_interfaces() {
                {{- if .ipDetection.preferredInterface }}
                echo "{{ .ipDetection.preferredInterface }}"
                {{- end }}
                ip route | grep default | awk '{print $5}' | sort -u | grep -v lo || echo ""
              }

              # 获取当前存储的 IP
              CURRENT_IP=$(kubectl get configmap {{ $root.Release.Name }}-dynamic-ip-config -o jsonpath='{.data.current-ip}' 2>/dev/null || echo "")
              echo "Current stored IP: $CURRENT_IP"

              # IP检测服务列表
              SERVICES=(
                "https://ping0.cc ping0.cc"
                "https://ipv4.icanhazip.com icanhazip"
                "https://ifconfig.info ifconfig.info"
              )

              # 检测新 IP
              NEW_IP=""

              # 方法1：使用网络接口检测
              {{- if .ipDetection.enableInterfaceDetection | default true }}
              INTERFACES=($(get_available_interfaces))
              if [[ ${#INTERFACES[@]} -gt 0 ]]; then
                echo "Trying interface-based detection with ${#INTERFACES[@]} interfaces"
                for interface in "${INTERFACES[@]}"; do
                  if [[ -n "$interface" ]]; then
                    echo "Trying interface: $interface"
                    for service_info in "${SERVICES[@]}"; do
                      service_url=$(echo "$service_info" | awk '{print $1}')
                      service_name=$(echo "$service_info" | awk '{print $2}')
                      NEW_IP=$(detect_ip_with_interface "$interface" "$service_url" "$service_name")
                      if [[ -n "$NEW_IP" ]]; then
                        echo "IP detected via interface $interface and service $service_name: $NEW_IP"
                        break 2
                      fi
                      sleep 1
                    done
                  fi
                done
              fi
              {{- end }}

              # 方法2：标准方式检测（不指定接口）
              if [[ -z "$NEW_IP" ]]; then
                echo "Trying standard detection methods"
                for service_info in "${SERVICES[@]}"; do
                  service_url=$(echo "$service_info" | awk '{print $1}')
                  service_name=$(echo "$service_info" | awk '{print $2}')
                  NEW_IP=$(detect_ip_standard "$service_url" "$service_name")
                  if [[ -n "$NEW_IP" ]]; then
                    echo "IP detected via standard method and service $service_name: $NEW_IP"
                    break
                  fi
                  sleep 2
                done
              fi

              if [[ -z "$NEW_IP" ]]; then
                echo "ERROR: Failed to detect IP address using all methods"
                exit 1
              fi
              
              echo "Detected IP: $NEW_IP"
              
              # 检查 IP 是否发生变化
              if [[ "$NEW_IP" != "$CURRENT_IP" ]]; then
                echo "IP changed from $CURRENT_IP to $NEW_IP"
                
                # 更新 ConfigMap
                kubectl patch configmap {{ $root.Release.Name }}-dynamic-ip-config --patch "{
                  \"data\": {
                    \"current-ip\": \"$NEW_IP\",
                    \"last-update\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"
                  }
                }"
                
                # 更新 well-known 配置
                {{- $internalServer := .internalServer }}
                SERVER_CONFIG="{\"m.server\": \"{{ $internalServer.subdomains.matrix }}.{{ $internalServer.baseHost }}:{{ $internalServer.port }}\"}"
                CLIENT_CONFIG="{
                  \"m.homeserver\": {
                    \"base_url\": \"https://{{ $internalServer.subdomains.matrix }}.{{ $internalServer.baseHost }}:{{ $internalServer.port }}\"
                  },
                  \"org.matrix.msc2965.authentication\": {
                    \"issuer\": \"https://{{ $internalServer.subdomains.mas }}.{{ $internalServer.baseHost }}:{{ $internalServer.port }}/\",
                    \"account\": \"https://{{ $internalServer.subdomains.mas }}.{{ $internalServer.baseHost }}:{{ $internalServer.port }}/account\"
                  },
                  \"org.matrix.msc4143.rtc_foci\": [
                    {
                      \"type\": \"livekit\",
                      \"livekit_service_url\": \"https://{{ $internalServer.subdomains.rtc }}.{{ $internalServer.baseHost }}:{{ $internalServer.port }}\"
                    }
                  ]
                }"
                
                # 更新 well-known ConfigMap
                kubectl patch configmap {{ $root.Release.Name }}-well-known-haproxy --patch "{
                  \"data\": {
                    \"server\": \"$SERVER_CONFIG\",
                    \"client\": \"$CLIENT_CONFIG\"
                  }
                }" || echo "Warning: Failed to update well-known config"
                
                echo "IP update completed successfully"
              else
                echo "IP unchanged, no update needed"
              fi
            env:
            - name: KUBECONFIG
              value: /var/run/secrets/kubernetes.io/serviceaccount
            volumeMounts:
            - name: service-account-token
              mountPath: /var/run/secrets/kubernetes.io/serviceaccount
              readOnly: true
          volumes:
          - name: service-account-token
            projected:
              sources:
              - serviceAccountToken:
                  path: token
              - configMap:
                  name: kube-root-ca.crt
                  items:
                  - key: ca.crt
                    path: ca.crt
              - downwardAPI:
                  items:
                  - path: namespace
                    fieldRef:
                      fieldPath: metadata.namespace

---

# ServiceAccount for the IP updater
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ $root.Release.Name }}-dynamic-ip-updater
  namespace: {{ $root.Release.Namespace }}
  labels:
    {{- include "element-io.dynamic-ip-updater.labels" (dict "root" $root "context" .) | nindent 4 }}

---

# ClusterRole for the IP updater
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ $root.Release.Name }}-dynamic-ip-updater
  labels:
    {{- include "element-io.dynamic-ip-updater.labels" (dict "root" $root "context" .) | nindent 4 }}
rules:
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "patch", "update"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list"]

---

# ClusterRoleBinding for the IP updater
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ $root.Release.Name }}-dynamic-ip-updater
  labels:
    {{- include "element-io.dynamic-ip-updater.labels" (dict "root" $root "context" .) | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ $root.Release.Name }}-dynamic-ip-updater
subjects:
- kind: ServiceAccount
  name: {{ $root.Release.Name }}-dynamic-ip-updater
  namespace: {{ $root.Release.Namespace }}

{{- end -}}
{{- end -}}
